<template>
  <div class="">
    <div class="statistics oil3 mtJ2 flex items-center">
      <div
        class="item"
        v-for="(item, index) in energyStatisticList"
        :key="index"
      >
        <img :src="item.img" />
        <span class="mlJ1">{{ item.title }}</span>
        <span class="mlJ1 textValue">
          {{ formatterText(energyStatisticInfo[item.value]) || "--" }}
        </span>
        <span>{{ energyStatisticInfo[item.unit] }}</span>
      </div>
    </div>
    <div class="shiftBox flex mtJ2">
      <div class="carousel">
        <div>
          <el-carousel
            v-if="energyStatisticInfo.teamGroupEnergyCardList?.length"
            height="180px"
            indicator-position="none"
            loop
            :autoplay="false"
          >
            <el-carousel-item v-for="k in carouselItemCount" :key="k">
              <div
                class="label"
                v-for="(item, index) in getCurrentGroupItems(k)"
                :key="index"
                :style="{
                  background: `url(${index % 2 === 0 ? bgimg2 : bgimg1})`
                }"
              >
                <div>{{ item.teamGroupName }}</div>
                <div class="flex items-center mtJ2">
                  <span class="name">用能占比</span>
                  <TextTooltip
                    class="textValue mlJ1"
                    :class="getTextColorStyle(index)"
                    placement="top"
                    :content="`${formatterText(item.energyProportion)}%`"
                    :text="`${formatterText(item.energyProportion)}`"
                  ></TextTooltip>
                  <span>%</span>
                </div>
                <div class="flex items-center mtJ1">
                  <span class="name">总能耗</span>
                  <TextTooltip
                    class="textValue mlJ1"
                    :class="getTextColorStyle(index)"
                    placement="top"
                    :content="`${formatterText(item.energyCount)}${
                      energyStatisticInfo.energyUnit
                    }`"
                    :text="`${formatterText(item.energyCount)}`"
                  ></TextTooltip>
                  <span>{{ energyStatisticInfo.energyUnit }}</span>
                </div>
                <div class="flex items-center mtJ1">
                  <span class="name">总班次</span>
                  <TextTooltip
                    class="textValue mlJ1"
                    style="max-width: 120px"
                    :class="getTextColorStyle(index)"
                    placement="top"
                    :content="`${formatterText(item.classesTotal)}`"
                    :text="`${formatterText(item.classesTotal)}`"
                  ></TextTooltip>
                </div>
                <div class="flex items-center mtJ1">
                  <span class="name">平均班次能耗</span>
                  <TextTooltip
                    class="textValue mlJ1"
                    style="max-width: 46px"
                    :class="getTextColorStyle(index)"
                    placement="top"
                    :content="`${formatterText(item.avgEnergy)}${
                      energyStatisticInfo.energyUnit
                    }`"
                    :text="`${formatterText(item.avgEnergy)}`"
                  ></TextTooltip>
                  <span>{{ energyStatisticInfo.energyUnit }}</span>
                </div>
              </div>
              <div
                class="label"
                v-for="i in 3 - getCurrentGroupItems(k).length"
                :key="'empty' + i"
              ></div>
            </el-carousel-item>
          </el-carousel>
          <div v-else class="noData">暂无数据</div>
        </div>
      </div>
      <div class="barChart">
        <CetChart
          :key="timestamp"
          :inputData_in="CetChart_1.inputData_in"
          v-bind="CetChart_1.config"
        />
      </div>
    </div>
  </div>
</template>

<script>
import TextTooltip from "../../components/textTooltip.vue";
export default {
  name: "ShiftStatistics",
  props: {
    energyStatisticInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: { TextTooltip },
  computed: {
    carouselItemCount() {
      const listLength =
        this.energyStatisticInfo.teamGroupEnergyCardList?.length || 0;
      return Math.ceil(Math.max(listLength, 3) / 3);
    },
    getCurrentGroupItems() {
      return k => {
        const list = this.energyStatisticInfo.teamGroupEnergyCardList || [];
        // 直接返回切片后的数据，不再添加空数据
        return list.slice((k - 1) * 3, k * 3);
      };
    },
    getTextColorStyle() {
      return index => {
        return index % 2 === 0 ? "oil2" : "oil6";
      };
    }
  },
  watch: {
    energyStatisticInfo: {
      handler(newVal) {
        this.timestamp = Date.now();
        if (newVal && newVal.teamGroupEnergyCardList?.length) {
          this.getChartData(newVal);
        } else {
          // 清空数据
          this.CetChart_1.config.options.series[0].data = [];
          this.CetChart_1.config.options.yAxis.data = [];
          this.CetChart_1.config.options.dataZoom = [];
        }
      },
      immediate: true,
      deep: true
    }
  },
  data() {
    return {
      timestamp: Date.now(),
      energyStatisticList: [
        {
          img: require("../../assets/energy.png"),
          title: $T("总班组能耗"),
          value: "energyTotal",
          unit: "energyUnit"
        },
        {
          img: require("../../assets/totalNumber.png"),
          title: $T("总班次"),
          value: "classesTotal",
          unit: $T("")
        },
        {
          img: require("../../assets/statistics.png"),
          title: $T("平均班次能耗"),
          value: "avgEnergy",
          unit: "energyUnit"
        }
      ],
      bgimg1: require("../../assets/bgimg1.png"),
      bgimg2: require("../../assets/bgimg2.png"),
      CetChart_1: {
        inputData_in: null,
        config: {
          options: {
            title: {
              text: "平均班次能耗排名",
              textStyle: {
                fontSize: 16,
                fontWeight: 500
              }
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow"
              }
            },
            legend: {},
            dataZoom: [],
            grid: {
              left: "3%",
              right: "8%",
              bottom: "3%",
              height: 140,
              containLabel: true
            },
            xAxis: {
              type: "value",
              name: "", // 添加x轴单位
              nameGap: 28,
              nameTextStyle: {
                align: "right",
                fontSize: 12
              }
            },
            yAxis: {
              type: "category",
              inverse: true,
              data: []
            },
            series: [
              {
                name: "",
                type: "bar",
                barWidth: 16,
                data: []
              }
            ]
          }
        }
      }
    };
  },
  methods: {
    getChartData(newVal) {
      const list = _.cloneDeep(newVal);
      const data = list.teamGroupEnergyCardList;
      data.forEach((item, index) => {
        item.order = index;
      });
      data.sort((a, b) => a.order - b.order);
      const avgEnergyList = data.map(res => res.avgEnergy);
      const teamGroupNameList = data.map(res => res.teamGroupName);
      this.CetChart_1.config.options.series[0].data = avgEnergyList;
      this.CetChart_1.config.options.yAxis.data = teamGroupNameList;
      this.CetChart_1.config.options.xAxis.name = newVal.energyUnit;
      this.CetChart_1.config.options.xAxis.nameTextStyle = {
        align: "right"
      };
      this.CetChart_1.config.options.tooltip.formatter = function (params) {
        return `${params[0].marker} ${params[0].name}: ${params[0].value} ${newVal.energyUnit}`;
      };

      // 调整柱状图宽度
      this.CetChart_1.config.options.series[0].barWidth = 10;
      // 添加滚动条
      if (data.length > 4) {
        // 当数据量超过 10 条时添加滚动条
        this.CetChart_1.config.options.dataZoom = [
          {
            type: "slider",
            yAxisIndex: 0,
            filterMode: "empty",
            start: 0,
            end: Math.min(100, (4 / data.length) * 100)
          }
        ];
      } else {
        this.CetChart_1.config.options.dataZoom = []; // 数据量较少时移除滚动条
        this.CetChart_1.config.options.grid.height = "85%";
      }
    },
    formatterText(val) {
      return val || val == 0 ? val : "--";
    }
  }
};
</script>

<style lang="scss" scoped>
.oil3 {
  @include themeify {
    background: rgba(themed(oil3), 0.1);
  }
}
.oil2 {
  @include themeify {
    color: rgba(themed(oil2), 1);
  }
}
.oil6 {
  @include themeify {
    color: rgba(themed(oil6), 1);
  }
}
.statistics {
  height: 40px;
  padding-left: 24px;
  box-sizing: border-box;
  img {
    vertical-align: -4px;
  }
  .item {
    margin-right: 40px;
    .textValue {
      font-family: Barlow, Barlow;
      @include font_color(T2);
      font-size: 18px;
    }
  }
}
.shiftBox {
  height: 100%;
  .carousel {
    width: 58%;
    height: 180px;
    :deep(.el-carousel__item) {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .label {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      padding: 20px 0 0 20px;
      box-sizing: border-box;
      margin-right: 8px;
      .name {
        @include font_color(T3);
      }
      .textValue {
        font-weight: 500;
        font-size: 16px;
        font-family: Barlow, Barlow;
        margin-right: 4px;
        max-width: 84px;
      }
    }
    .noData {
      text-align: center;
      line-height: 180px;
      @include font_color(T4);
    }
  }
  .barChart {
    width: 42%;
    height: 180px;
  }
}
</style>
