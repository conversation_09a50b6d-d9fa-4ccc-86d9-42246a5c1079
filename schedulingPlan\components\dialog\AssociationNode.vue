<template>
  <!-- 1弹窗组件 -->
  <CetDialog
    v-bind="CetDialog_1"
    v-on="CetDialog_1.event"
    class="CetDialog small"
  >
    <div class="eem-cont-c1">
      <el-checkbox class="mbJ3" v-model="checked" @change="checkedChange">
        {{ $T("默认勾选子节点") }}
      </el-checkbox>
      <CetGiantTree
        class="giantTree"
        ref="giantTree1"
        v-show="!checked"
        v-bind="CetGiantTree_1"
        v-on="CetGiantTree_1.event"
      ></CetGiantTree>
      <CetGiantTree
        class="giantTree"
        ref="giantTree2"
        v-show="checked"
        v-bind="CetGiantTree_2"
        v-on="CetGiantTree_2.event"
      ></CetGiantTree>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>
<script>
import { httping } from "@omega/http";
import commonApi from "@/api/custom.js";
import { getTreeParams } from "eem-utils/analysisServiceConfig.js";
export default {
  name: "AssociationNode",
  components: {},
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object
    },
    schemeInfo: {
      type: Object
    }
  },

  computed: {
    projectId() {
      return this.$store.state.projectId;
    }
  },

  data() {
    return {
      currentNode: null,
      CetDialog_1: {
        title: $T("关联节点"),
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        showClose: true,
        event: {}
      },
      checked: false,
      CetGiantTree_1: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            chkboxType: { Y: "", N: "" },
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_1_checkedNodes_out //勾选节点输出
        }
      },
      CetGiantTree_2: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        unCheckTrigger_in: new Date().getTime(),
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          checkedNodes_out: this.CetGiantTree_2_checkedNodes_out //勾选节点输出
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("关闭"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.checked = false;
      this.getTreeData();
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },

  methods: {
    checkedChange() {
      const checkNodes = this._.cloneDeep(this.checkNodes);
      // 封装滚动逻辑
      const scrollTreeToTopLeft = treeRef => {
        const ztreeElement = treeRef?.$el?.querySelector(".ztree");
        if (ztreeElement) {
          ztreeElement.scrollTop = 0;
          ztreeElement.scrollLeft = 0;
        }
      };
      // 封装设置节点勾选和展开逻辑
      const setAndExpandNodes = (tree, treeRef, nodes) => {
        tree.checkedNodes = nodes;
        if (treeRef?.ztreeObj) {
          this.expandNode(nodes, "tree_id", treeRef.ztreeObj);
        }
        if (!nodes.length) {
          tree.unCheckTrigger_in = new Date().getTime();
        }
      };
      setTimeout(() => {
        // 滚动树组件到顶部和左侧
        scrollTreeToTopLeft(this.$refs.giantTree1);
        scrollTreeToTopLeft(this.$refs.giantTree2);
        if (this.checked) {
          setAndExpandNodes(
            this.CetGiantTree_2,
            this.$refs.giantTree2,
            checkNodes
          );
        } else {
          setAndExpandNodes(
            this.CetGiantTree_1,
            this.$refs.giantTree1,
            checkNodes
          );
        }
      }, 0);
    },
    // 获取节点树
    getTreeData() {
      const data = {
        rootID: this.projectId,
        rootLabel: "project",
        subLayerConditions: getTreeParams(1),
        treeReturnEnable: true
      };
      httping({
        url: "/eem-service/v1/node/nodeTree/simple",
        method: "POST",
        data
      }).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          const EXCLUDED_MODEL_LABEL = "linesegmentwithswitch";
          // 递归函数，用于过滤节点及其子节点
          const filterNode = node => {
            const newNode = this._.cloneDeep(node);
            newNode.children = [];
            if (node.children && node.children.length > 0) {
              newNode.children = node.children
                .filter(child => child.modelLabel !== EXCLUDED_MODEL_LABEL)
                .map(filterNode);
            }
            return newNode;
          };
          const filteredData = res.data.map(filterNode);
          this.getNodeByTSScheme(filteredData);
        }
      });
    },
    // 获取关联节点
    getNodeByTSScheme(treeData) {
      this.checkNodes = [];
      const params = {
        schedulingSchemeId: this.schemeInfo.id
      };
      commonApi.querySchedulingschemeRelatedNode(params).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          const checkedNodes = res?.data?.map(item => ({
            id: item.id,
            modelLabel: item.modelLabel,
            tree_id: `${item.modelLabel}_${item.id}`
          }));
          this.CetGiantTree_1.checkedNodes = this._.cloneDeep(checkedNodes);
          this.checkNodes = checkedNodes;
          const expandNodes = () => {
            this.expandNode(
              checkedNodes,
              "tree_id",
              this.$refs.giantTree1.ztreeObj
            );
            this.expandNode(
              checkedNodes,
              "tree_id",
              this.$refs.giantTree2.ztreeObj
            );
          };
          setTimeout(expandNodes, 0);
        } else {
          const clearCheckedNodes = tree => {
            tree.checkedNodes = [];
            tree.unCheckTrigger_in = new Date().getTime();
          };
          clearCheckedNodes(this.CetGiantTree_1);
          clearCheckedNodes(this.CetGiantTree_2);
        }
        this.CetGiantTree_1.inputData_in = treeData;
        this.CetGiantTree_2.inputData_in = treeData;
      });
    },
    // 展开节点
    expandNode(nodes, key, ztreeObj) {
      setTimeout(() => {
        nodes.forEach(item => {
          const node = ztreeObj.getNodeByParam(key, item[key]);
          let parentNodes = [],
            parentNode = node && node.getParentNode();
          while (parentNode) {
            parentNodes.push(parentNode);
            parentNode = parentNode.getParentNode();
          }
          parentNodes.forEach(i => {
            ztreeObj.expandNode(i, true);
          });
        });
        $(this.$refs.giantTree1.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree1.$el).find(".ztree").scrollLeft(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollTop(0);
        $(this.$refs.giantTree2.$el).find(".ztree").scrollLeft(0);
      }, 0);
    },
    CetGiantTree_1_checkedNodes_out(val) {
      this.checkNodes = this._.cloneDeep(val);
    },
    CetGiantTree_2_checkedNodes_out(val) {
      this.checkNodes = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_confirm_statusTrigger_out(val) {
      const data = this.checkNodes.map(item => ({
        id: item.id,
        modelLabel: item.modelLabel,
        name: item.name
      }));
      const params = {
        schedulingSchemeId: this.schemeInfo.id,
        nodeList: data
      };
      commonApi.saveSchedulingschemeRelatedNode(params).then(res => {
        if (res.code === 0) {
          this.$message({
            message: $T("保存成功"),
            type: "success"
          });
          this.$emit("confirm_out");
          this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.CetDialog {
  .giantTree {
    height: 400px;
  }
}
</style>
