<template>
  <CetDialog class="CetDialog" v-bind="CetDialog_1">
    <div class="eem-cont-c1" style="padding-bottom: 1px; flex: 1">
      <CetForm
        ref="CetForm"
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
        class="flex-column"
        :key="visibleTrigger_in"
        style="height: calc(100% - 16px)"
      >
        <el-form-item label="排班方案名称" prop="name">
          <ElInput
            v-model.trim="CetForm_1.data.name"
            v-bind="ElInput_input_name"
            v-on="ElInput_input_name.event"
            maxlength="20"
          ></ElInput>
        </el-form-item>
        <el-form-item label="班组类型" prop="classTeamType">
          <el-radio-group
            v-model="CetForm_1.data.classTeamType"
            :disabled="isRadioGroupDisabled"
          >
            <el-radio :label="1">运维班组</el-radio>
            <el-radio :label="2">生产班组</el-radio>
          </el-radio-group>
        </el-form-item>
      </CetForm>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="mlJ1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
import commonApi from "@/api/custom.js";
export default {
  name: "AddOrEditScheduling",
  props: {
    visibleTrigger_in: Number,
    closeTrigger_in: Number,
    inputData_in: Object,
    isAdd: Boolean
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo;
    },
    isRadioGroupDisabled() {
      const hasClassesSchemeList =
        this.inputData_in?.classesSchemeList?.length > 0;
      const hasTeamGroupInfoList =
        this.inputData_in?.teamGroupInfoList?.length > 0;
      return hasClassesSchemeList || hasTeamGroupInfoList;
    }
  },
  data() {
    return {
      CetDialog_1: {
        showClose: true,
        width: "480px",
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime()
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: { classTeamType: 1 },
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: "请输入排班方案名称,且不超过20个字符",
              max: 20,
              trigger: ["blur"]
            }
          ],
          classTeamType: [
            {
              required: true,
              message: "请选择班组类型",
              trigger: ["blur", "change"]
            }
          ]
        },
        event: { saveData_out: this.CetForm_1_saveData_out }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_input_name: {
        value: "",
        placeholder: "请输入",
        style: {
          width: "100%"
        },
        event: {}
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    async visibleTrigger_in(val) {
      this.CetDialog_1.title = `${this.isAdd ? "新增" : "修改"}排班方案`;
      this.CetDialog_1.openTrigger_in = val;
      this.CetForm_1.data = this.isAdd
        ? { classTeamType: 1 }
        : this.inputData_in;
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    }
  },
  methods: {
    //弹窗确认按钮
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },
    //弹窗取消按钮
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },
    // 表单保存
    async CetForm_1_saveData_out() {
      const saveData = {
        name: this.CetForm_1.data.name,
        classTeamType: this.CetForm_1.data.classTeamType,
        createTime: new Date().getTime(),
        operator: this.userInfo.id,
        id: this.isAdd ? "" : this.inputData_in.id
      };
      const res = await commonApi.addOrUpdateSchedulingscheme(saveData);
      if (res.code !== 0) return;
      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.$emit("save_out");
      this.CetDialog_1.closeTrigger_in = Date.now();
    }
  }
};
</script>

<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  :deep(.el-dialog__body) {
    display: flex;
    height: 200px;
  }
  :deep(.el-form-item) {
    .el-form-item__label {
      line-height: 24px;
      text-align: left;
    }
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
</style>
