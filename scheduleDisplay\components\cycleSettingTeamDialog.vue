<template>
  <CetDialog class="CetDialog" v-bind="CetDialog_1" :before-close="beforeClose">
    <div class="eem-cont-c1" style="padding-bottom: 1px; flex: 1">
      <CetForm
        ref="CetForm"
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
        class="flex-column"
        :key="visibleTrigger_in"
        style="height: calc(100% - 16px)"
      >
        <el-form-item>
          <div class="tipsBox">
            循环设置说明：按照选定的循环班组，针对时间段内所选区间的开始和结束班次，进行自动排班。使用前请确保已经设置了所需日期的班次方案
          </div>
        </el-form-item>
        <el-form-item label="选择循环班组" prop="teamStartTime">
          <el-date-picker
            v-model="CetForm_1.data.teamStartTime"
            @change="changeDate"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-value="defaultValue"
          ></el-date-picker>
        </el-form-item>
        <div class="teamPreview">
          <!-- 班组颜色列表 -->
          <div class="flex items-center team" v-if="teamColorList.length">
            <div
              class="item mrJ1"
              v-for="(item, index) in teamColorList"
              :key="index"
            >
              <span class="color-marker" :class="item.color"></span>
              {{ item.name }}
            </div>
          </div>
          <!-- 循环班组预览 -->
          <div class="previewBox plJ2 prJ2">
            <div class="name mtJ1 mbJ1">循环班组预览</div>
            <div
              class="reserve mbJ1 flex items-center"
              v-if="teamGroupList.length"
            >
              <div
                class="reserve-container"
                :style="{
                  '--item-count':
                    teamGroupList.length > 24 ? 24 : teamGroupList.length
                }"
              >
                <div
                  class="reserve-item flex-1"
                  v-for="(teamGroup, teamIndex) in teamGroupList"
                  :key="teamGroup.logTime"
                >
                  <div class="name">{{ teamIndex + 1 }}</div>
                  <div
                    class="item"
                    v-for="(
                      scheduleConfig, configIndex
                    ) in teamGroup.schedulingClassesConfigVOS"
                    :key="`config-${scheduleConfig.teamGroupId}-${configIndex}`"
                  >
                    <div
                      class="colorBox"
                      :class="scheduleConfig.color || 'noRelation'"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
            <div class="reserve mbJ1 reserveBgc" v-else>
              请在上方选择对应日期
            </div>
          </div>
          <!-- 统计信息 -->
          <div
            class="flex items-center mtJ1 statistics"
            v-if="teamColorList.length"
          >
            <div v-html="getTeamCountString()"></div>
            <div class="mlJ1" v-if="!isAllCountEqual">
              <omega-icon class="tipsIcon icon-size-I0" symbolId="attention" />
              <span class="tipsName">目前各班组值班数量不平均</span>
            </div>
          </div>
        </div>
        <el-form-item label="选择要循环的时段" prop="teamTargetTime">
          <el-date-picker
            v-model="CetForm_1.data.teamTargetTime"
            value-format="yyyy-MM-dd"
            @change="selectTeamsTimeChange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-value="defaultValue"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>
        <el-form-item :label="$T('开始班次')" prop="startShift">
          <ElSelect
            @change="$forceUpdate()"
            style="width: 100%"
            v-model="CetForm_1.data.startShift"
            v-bind="CetForm_1.data"
            v-on="CetForm_1.data.event"
          >
            <ElOption
              v-for="item in ElOption_startShift.options_in"
              :key="item[ElOption_startShift.key]"
              :label="item[ElOption_startShift.label]"
              :value="item[ElOption_startShift.value]"
              :disabled="item[ElOption_startShift.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
        <el-form-item :label="$T('结束班次')" prop="endShift">
          <ElSelect
            @change="$forceUpdate()"
            style="width: 100%"
            v-model="CetForm_1.data.endShift"
            v-bind="CetForm_1.data"
            v-on="CetForm_1.data.event"
          >
            <ElOption
              v-for="item in ElOption_endShift.options_in"
              :key="item[ElOption_endShift.key]"
              :label="item[ElOption_endShift.label]"
              :value="item[ElOption_endShift.value]"
              :disabled="item[ElOption_endShift.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
      </CetForm>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="mlJ1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
import commonApi from "@/api/custom.js";
export default {
  name: "cycleSettingTeamDialog",
  props: {
    visibleTrigger_in: Number,
    closeTrigger_in: Number,
    inputData_in: Object,
    schedulingSchemeId: [Number, String],
    selectedDate: [Date, Number],
    schedulingTable: Array
  },
  data() {
    return {
      defaultValue: [
        this.$moment(this.selectedDate).startOf("month"),
        this.$moment(this.selectedDate).endOf("month")
      ],
      teamColorList: [],
      CetDialog_1: {
        showClose: true,
        width: "640px",
        title: "循环设置班组",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime()
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          teamStartTime: [
            {
              required: true,
              message: "请选择循环班组时间",
              trigger: ["blur", "change"]
            }
          ],
          teamTargetTime: [
            {
              required: true,
              message: "请选择要循环班组时间",
              trigger: ["blur", "change"]
            }
          ],
          startShift: [
            {
              required: true,
              message: "请选择开始班次",
              trigger: ["blur", "change"]
            }
          ],
          endShift: [
            {
              required: true,
              message: "请选择结束班次",
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElOption_startShift: {
        options_in: [],
        key: "classesConfigId",
        value: "classesConfigId",
        label: "classesConfigName",
        disabled: "disabled"
      },
      ElOption_endShift: {
        options_in: [],
        key: "classesConfigId",
        value: "classesConfigId",
        label: "classesConfigName",
        disabled: "disabled"
      },
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.selectDate = minDate.getTime();
          if (maxDate) {
            this.selectDate = "";
          }
        },
        disabledDate: time => {
          // 获取 selectedDate 的当月第一天和最后一天
          const startOfMonth = this.$moment(this.selectedDate)
            .startOf("month")
            .valueOf();
          const endOfMonth = this.$moment(this.selectedDate)
            .endOf("month")
            .valueOf();
          // 获取当前时间戳
          const currentTime = time.getTime();
          // 判断当前时间是否在当月范围内
          return currentTime < startOfMonth || currentTime > endOfMonth;
        }
      },
      teamGroupList: [],
      isAllCountEqual: true
    };
  },
  watch: {
    /**
     *  弹窗页面默认和弹窗的交互
     */
    async visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    //设置默认月份
    selectedDate: {
      handler(newVal) {
        this.defaultValue = [
          this.$moment(newVal).startOf("month"),
          this.$moment(newVal).endOf("month")
        ];
      }
    }
  },
  methods: {
    /**
     *  选择时间范围获取班组预览数据
     */
    async changeDate(e) {
      const valid = e?.length === 2;
      if (!valid) {
        this.teamGroupList = [];
        this.teamColorList = [];
        return;
      }
      const { startTimeStamp, endTimeStamp } = this.getDateTimestamps(e);
      const res = await this.queryTeamGroupInfo(startTimeStamp, endTimeStamp);
      if (res.code !== 0) {
        return;
      }
      const startDate = this.$moment(e[0]);
      const endDate = this.$moment(e[1]);
      const isCrossMonth = startDate.month() !== endDate.month();
      const list = (this.schedulingTable ?? []).filter(({ logTime }) => {
        return logTime >= startTimeStamp && logTime < endTimeStamp;
      });
      let dataToProcess;
      if (isCrossMonth) {
        dataToProcess = [
          ...list,
          ...res.data.filter(
            item => !list.some(l => l.logTime === item.logTime)
          )
        ];
      } else {
        dataToProcess = list.length ? list : res.data;
      }
      this.processTeamGroupData(dataToProcess);
    },

    /**
     *  处理班组数据
     */
    processTeamGroupData(dataSource) {
      if (dataSource.length) {
        const uniqueMap = new Map();
        dataSource.forEach(item => {
          uniqueMap.set(item.logTime, item);
        });
        const uniqueList = Array.from(uniqueMap.values());
        this.teamGroupList = uniqueList.sort((a, b) => a.logTime - b.logTime);
        this.teamColorList = this.getUniqueColors(this.teamGroupList);
        this.getTeamCountString();
      } else {
        this.teamGroupList = [];
        this.teamColorList = [];
      }
    },

    /**
     *  获取日期范围的时间戳
     */
    getDateTimestamps(e) {
      const startDate = this.$moment(e[0]).startOf("day");
      const endDate = this.$moment(e[1]).startOf("day").add(1, "days");
      const startTimeStamp = startDate.valueOf();
      const endTimeStamp = endDate.valueOf();
      return { startTimeStamp, endTimeStamp };
    },

    /**
     *  查询班组信息
     */
    async queryTeamGroupInfo(startTimeStamp, endTimeStamp) {
      return await commonApi.querySchedulingclassesTeamgroupinfo({
        schedulingSchemeId: this.schedulingSchemeId,
        startTime: startTimeStamp,
        endTime: endTimeStamp
      });
    },

    /**
     *  从班组列表中获取唯一的颜色及其对应的班组名称
     */
    getUniqueColors(teamGroupList) {
      const colors = [];
      teamGroupList.forEach(item => {
        item.schedulingClassesConfigVOS.forEach((config, index) => {
          if (config.color && !colors.some(c => c.color === config.color)) {
            colors.push({
              name: config.teamGroupName,
              color: config.color,
              order: config.order ? config.order : index + 1
            });
          }
        });
      });
      return colors;
    },

    /**
     *  根据要循环班组时间获取开始和结束班次
     */
    selectTeamsTimeChange(e) {
      if (e && e.length === 2) {
        const startDate = this.$moment(e[0]).startOf("day").valueOf();
        const endDate = this.$moment(e[1]).startOf("day").valueOf();
        const startShift = this.schedulingTable.find(
          item => item.logTime === startDate
        );
        const endShift = this.schedulingTable.find(
          item => item.logTime === endDate
        );
        this.setShiftOptions(startShift, endShift);
      } else {
        this.resetShiftOptions();
      }
    },

    /**
     * 设置开始班次和结束班次的选项，并初始化表单中的开始班次和结束班次的值
     */
    setShiftOptions(startShift, endShift) {
      this.ElOption_startShift.options_in =
        startShift?.schedulingClassesConfigVOS || [];
      this.ElOption_endShift.options_in =
        endShift?.schedulingClassesConfigVOS || [];

      this.CetForm_1.data.startShift =
        this.ElOption_startShift.options_in.length > 0
          ? this.ElOption_startShift.options_in[0].classesConfigId
          : this.$set(this.CetForm_1.data, "startShift", "");

      this.CetForm_1.data.endShift =
        this.ElOption_endShift.options_in.length > 0
          ? this.ElOption_endShift.options_in[
              this.ElOption_endShift.options_in.length - 1
            ].classesConfigId
          : this.$set(this.CetForm_1.data, "endShift", "");
    },

    /**
     * 重置班次选项和表单值
     */
    resetShiftOptions() {
      this.$set(this.CetForm_1.data, "startShift", "");
      this.$set(this.CetForm_1.data, "endShift", "");
      this.$set(this.ElOption_startShift, "options_in", []);
      this.$set(this.ElOption_endShift, "options_in", []);
    },

    /**
     * 弹窗确认按钮
     */
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },

    /**
     * 弹窗取消按钮
     */
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
      this.clearFormData();
    },

    /**
     * 弹窗关闭前触发的事件
     */
    beforeClose() {
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
      this.clearFormData();
    },

    /**
     * 表单保存
     */
    async CetForm_1_saveData_out() {
      const { teamStartTime, teamTargetTime, startShift, endShift } =
        this.CetForm_1.data;
      // 判断当前日期内有无设置班次
      const startTimestamp = this.$moment(teamTargetTime[0])
        .startOf("day")
        .valueOf();
      const endTimestamp = this.$moment(teamTargetTime[1])
        .startOf("day")
        .add(1, "days")
        .valueOf();
      const hasData = this.schedulingTable.some(item => {
        return item.logTime >= startTimestamp && item.logTime < endTimestamp;
      });

      if (!hasData) {
        this.$message.warning("请设置要循环班组范围的数据!");
        return;
      }
      // 如果teamTargetTime的开始和结束时间不在同一个月
      if (
        this.$moment(teamTargetTime[0]).month() !==
        this.$moment(teamTargetTime[1]).month()
      ) {
        // 将开始时间设置为selectedDate月份的第一天
        const firstDayOfMonth = this.$moment(this.selectedDate)
          .startOf("month")
          .format("YYYY-MM-DD");
        teamTargetTime[0] = firstDayOfMonth;
      }

      const teamGroupListWithOrder = this.teamGroupList.map(item => {
        return {
          ...item,
          schedulingClassesConfigVOS: item.schedulingClassesConfigVOS.map(
            (config, index) => {
              return {
                ...config,
                order: config.order || index + 1
              };
            }
          )
        };
      });
      this.$emit("confirmForm", {
        teamGroupList: teamGroupListWithOrder.length
          ? teamGroupListWithOrder
          : [],
        teamStartTime,
        teamTargetTime,
        startShift,
        endShift
      });
      this.clearFormData();
      this.CetDialog_1.closeTrigger_in = Date.now();
    },

    /**
     * 清除表单和相关数据的方法
     */
    clearFormData() {
      this.teamGroupList = [];
      this.teamColorList = [];
      this.CetForm_1.data = {};
      this.ElOption_endShift.options_in = [];
      this.ElOption_startShift.options_in = [];
    },

    /**
     * 统计班组名称的数量
     */
    countTeamNames() {
      const teamCount = {};
      this.teamGroupList.forEach(item => {
        item.schedulingClassesConfigVOS.forEach(config => {
          const teamName = config.teamGroupName;
          if (teamName) {
            teamCount[teamName] = (teamCount[teamName] || 0) + 1;
          }
        });
      });
      return teamCount;
    },

    /**
     * 获取统计班组名称的字符串
     */
    getTeamCountString() {
      const teamCount = this.countTeamNames();
      const names = Object.keys(teamCount);
      // 新增：检查所有count是否相等
      const counts = Object.values(teamCount);
      this.isAllCountEqual = counts.every(count => count === counts[0]);
      return names
        .map((name, index) => {
          const count = teamCount[name];
          const separator = index < names.length - 1 ? ", " : "";
          return `<span class="name">${name}:</span> <span class="count">${count}次</span>${separator}`;
        })
        .join("");
    }
  }
};
</script>

<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  :deep(.el-dialog__body) {
    display: flex;
  }
  :deep(.el-form-item) {
    .el-form-item__label {
      line-height: 24px;
      text-align: left;
    }
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
  .tipsBox {
    @include themeify {
      background: rgba(themed(Sta4), 0.1);
    }
    @include font_color(Sta4);
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    border-radius: 4px;
    padding: 10px;
    box-sizing: border-box;
  }
  .teamPreview {
    margin-bottom: 18px;
    .team {
      max-width: 572px;
      overflow-x: auto;
      height: 30px;
      .item {
        @include font_color(T4);
        white-space: nowrap;
      }
      .color-marker {
        display: inline-block;
        width: 12px;
        height: 12px;
        vertical-align: -1px;
        margin-right: 4px;
      }
    }
    .previewBox {
      border: 1px solid;
      @include border_color(T4);
      border-radius: 4px;
      box-sizing: border-box;
      .name {
        height: 20px;
        line-height: 20px;
        @include font_color(T4);
      }
      .reserve {
        @include font_color(T4);
        text-align: center;
        line-height: 20px;
        box-sizing: border-box;
        .reserve-container {
          display: flex;
          width: 100%;
          overflow-x: auto;
          max-width: calc(
            24 * (100% / var(--item-count))
          ); // 当数量大于 24 时出现滚动条
        }

        .reserve-item {
          flex: 0 0 calc(100% / var(--item-count)); // 根据数量平分宽度
          min-width: calc(100% / 24);
        }
        .item {
          margin-right: 4px;
        }
        .colorBox {
          display: inline-block;
          width: 100%;
          height: 20px;
        }
      }
      .reserveBgc {
        padding: 10px;
        @include background_color(BG2);
      }
    }
    :deep(.statistics .name) {
      @include font_color(T4);
    }
    :deep(.statistics .count) {
      @include font_color(T1);
    }
  }
  .tipsName,
  .tipsIcon {
    @include font_color(Sta2);
    margin-right: 4px;
  }
  .tipsIcon {
    vertical-align: -2px;
  }
  .tipsName {
    font-size: 12px;
    vertical-align: 1px;
  }
  .noRelation {
    @include background_color(BG2);
  }
}
@import "../../assets/bgc.scss";
</style>
