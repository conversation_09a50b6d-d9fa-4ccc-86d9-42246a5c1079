<template>
  <el-container class="page flex">
    <div class="leftContent">
      <el-header class="p0 mtJ2 mbJ2" height="40px">
        <CetButton
          class="fl mrJ1"
          v-bind="CetButton_add"
          v-on="CetButton_add.event"
        ></CetButton>
        <ElInput
          class="fl mrJ1"
          v-model.trim="ElInput_searchContent.value"
          v-bind="ElInput_searchContent"
          v-on="ElInput_searchContent.event"
        ></ElInput>
        <customSelect
          class="fl mrJ1"
          v-model="ElSelect_teamType.value"
          v-bind="ElSelect_teamType"
          v-on="ElSelect_teamType.event"
          prefix_in="班组类型"
        >
          <ElOption
            v-for="item in ElOption_teamType.options_in"
            :key="item[ElOption_teamType.key]"
            :label="item[ElOption_teamType.label]"
            :value="item[ElOption_teamType.value]"
            :disabled="item[ElOption_teamType.disabled]"
          ></ElOption>
        </customSelect>
      </el-header>
      <el-main class="p0 mainBox">
        <CetTable
          class="piemTableBox"
          :data.sync="CetTable_1.data"
          :dynamicInput.sync="CetTable_1.dynamicInput"
          v-bind="CetTable_1"
          v-on="CetTable_1.event"
        >
          <template v-for="item in Columns_1">
            <ElTableColumn :key="item.label" v-bind="item"></ElTableColumn>
          </template>
          <ElTableColumn
            label="操作"
            width="180"
            header-align="left"
            align="left"
          >
            <template slot-scope="scope">
              <div style="display: flex; white-space: nowrap">
                <span class="handle" @click.stop="editHandle(scope.row)">
                  编辑
                </span>
                <span
                  v-if="scope.row.classTeamType === 1"
                  class="handle del mlJ3"
                  @click.stop="deleteHandle(scope.row)"
                >
                  删除
                </span>
                <el-dropdown
                  v-else
                  @command="handleCommand"
                  class="mlJ3 cursor"
                  trigger="click"
                >
                  <div class="showBtn">...</div>
                  <el-dropdown-menu slot="dropdown" class="dropdown">
                    <el-dropdown-item
                      :command="{ val: 'correlation', scope }"
                      class="handle cursor"
                    >
                      关联节点
                    </el-dropdown-item>
                    <el-dropdown-item
                      class="cursor"
                      :command="{ val: 'del', scope }"
                    >
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </template>
          </ElTableColumn>
        </CetTable>
        <el-pagination
          class="mtJ1"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-sizes="pageSizes"
          :page-size.sync="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
        ></el-pagination>
      </el-main>
    </div>
    <div class="rightContent">
      <el-tabs v-model="activeName" class="topTabs" @tab-click="onTabClick">
        <el-tab-pane label="班次方案" name="shiftPlan"></el-tab-pane>
        <el-tab-pane label="班组列表" name="teamList"></el-tab-pane>
      </el-tabs>
      <ShiftPlan
        v-if="activeName === 'shiftPlan'"
        :schemeInfo="schedulingSchemeInfo"
        :refreshTrigger_in="refreshTrigger_in"
        :tableList="CetTable_1.data"
        @refreshTable="refreshTable"
      ></ShiftPlan>
      <TeamList
        v-if="activeName === 'teamList'"
        :refreshTrigger_in="refreshTrigger_in"
        :schemeInfo="schedulingSchemeInfo"
        :tableList="CetTable_1.data"
        @refreshTable="refreshTable"
      ></TeamList>
    </div>
    <AddOrEditScheduling v-bind="addOrEdit" @save_out="getTableData" />
    <AssociationNode
      v-bind="associationNode"
      :schemeInfo="schedulingSchemeInfo"
      @confirm_out="getTableData"
    ></AssociationNode>
  </el-container>
</template>

<script>
import commonApi from "@/api/custom.js";
import AddOrEditScheduling from "./components/dialog/AddOrEditScheduling.vue";
import ShiftPlan from "./components/page/shiftPlan.vue";
import TeamList from "./components/page/teamList.vue";
import customSelect from "eem-components/customElSelect";
import AssociationNode from "./components/dialog/AssociationNode.vue";
export default {
  name: "schedulingPlan",
  components: {
    customSelect,
    ShiftPlan,
    TeamList,
    AddOrEditScheduling,
    AssociationNode
  },
  data() {
    return {
      totalCount: 0,
      currentPage: 1,
      pageSizes: [10, 20, 50, 100],
      pageSize: 20,
      pageTotal: 0,
      activeName: "shiftPlan",
      CetButton_add: {
        class: "fr",
        visible_in: true,
        disable_in: false,
        title: "新增排班方案",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_teamPlan_out
        }
      },
      ElInput_searchContent: {
        value: "",
        clearable: true,
        placeholder: "请输入内容",
        "suffix-icon": "el-icon-search",
        style: {
          width: "240px"
        },
        event: {
          change: this.getTableData
        }
      },
      ElSelect_teamType: {
        value: "",
        style: {
          width: "240px"
        },
        event: {
          change: this.getTableData
        }
      },
      ElOption_teamType: {
        options_in: [
          {
            id: "",
            name: "全部",
            disabled: false
          },
          {
            id: 1,
            name: "运维班组",
            disabled: false
          },
          {
            id: 2,
            name: "生产班组",
            disabled: false
          }
        ],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        "empty-text": "列表暂无数据",
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {
          pageSize: 10,
          layout: "total,sizes, prev, pager, next, jumper"
        },
        exportFileName: "",
        highlightCurrentRow: true,
        event: {
          record_out: this.CetTable_message_record_out
        }
      },
      Columns_1: [
        {
          type: "index",
          label: "序号",
          showOverflowTooltip: true,
          width: "50"
        },
        {
          prop: "name", // 支持path a[0].b
          label: "方案名称",
          minWidth: "160",
          showOverflowTooltip: true
        },
        {
          prop: "classTeamType", // 支持path a[0].b
          label: "班组类型",
          minWidth: "160",
          showOverflowTooltip: true,
          formatter: (row, column, cellValue, index) => {
            return cellValue === 1 ? "运维班组" : "生产班组";
          }
        }
      ],
      addOrEdit: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        isAdd: null
      },
      associationNode: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      schedulingSchemeInfo: {},
      refreshTrigger_in: new Date().getTime()
    };
  },
  created() {
    this.getTableData();
  },
  activated() {
    this.ElInput_searchContent.value = "";
    this.ElSelect_teamType.value = "";
    this.getTableData();
  },
  methods: {
    /**
     * 切换tab
     */
    onTabClick() {
      this.refreshTrigger_in = new Date().getTime();
      this.getTableData();
    },

    /**
     * 获取表格数据
     */
    async getTableData() {
      let params = {
        key: this.ElInput_searchContent.value,
        classTeamType: this.ElSelect_teamType.value,
        index: this.currentPage,
        size: this.pageSize
      };
      let res = await commonApi.querySchedulingscheme(params);
      if (res.code === 0) {
        this.totalCount = res.total;
        this.refreshTrigger_in = Date.now();
        this.CetTable_1.data = _.get(res, "data", []);
      }
    },

    /**
     * 新增排班方案按钮
     */
    CetButton_add_teamPlan_out() {
      this.addOrEdit.inputData_in = null;
      this.addOrEdit.isAdd = true;
      this.addOrEdit.visibleTrigger_in = Date.now();
    },

    /**
     * 编辑排班方案按钮
     */
    editHandle(row) {
      this.addOrEdit.inputData_in = row;
      this.addOrEdit.isAdd = false;
      this.addOrEdit.visibleTrigger_in = +new Date();
      // });
    },

    /**
     * 下拉菜单操作按钮
     */
    handleCommand({ val, scope }) {
      if (val === "correlation") {
        this.associationNode.visibleTrigger_in = +new Date();
      } else if (val === "del") {
        this.deleteHandle(scope.row);
      }
    },

    /**
     * 删除排班方案
     */
    deleteHandle(row) {
      this.$confirm("确定要删除所选项吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        const res = await commonApi.deleteSchedulingscheme({ id: row.id });
        if (res.code !== 0) return;
        this.$message({
          message: $T("删除成功"),
          type: "success"
        });
        this.getTableData();
      });
    },

    /**
     * 更改每页条数
     */
    handleSizeChange(val) {
      this.currentPage = 1;
      this.pageSize = val;
      this.getTableData();
    },

    /**
     * 更改当前页
     */
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getTableData();
    },

    /**
     * 选中table当前行
     */
    CetTable_message_record_out(val) {
      this.schedulingSchemeInfo = val;
    },

    /**
     * 班组、班次数据发生改变时刷新数据
     */
    refreshTable() {
      this.getTableData();
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  @include background-color(BG1);

  .leftContent {
    width: 60%;
    padding: 0 24px;
    box-sizing: border-box;
    border-right: 1px solid;
    border-color: #f0f0f0;
    .mainBox {
      height: calc(100% - 72px);
      .el-container {
        height: calc(100% - 42px);
      }
    }
    .el-pagination {
      text-align: right;
    }

    .showBtn {
      width: 24px;
      height: 24px;
      line-height: 10px;
      text-align: center;
      font-size: 24px;
      // @include background-color(BG1);
      @include font_color(T1);
    }
  }
  .rightContent {
    width: 40%;
    padding: 0 24px;
    box-sizing: border-box;
    overflow-y: auto;
  }
  .handle {
    cursor: pointer;
    @include font_color(ZS);
    &.del {
      @include font_color(Sta3);
    }
  }
  .cursor {
    cursor: pointer;
  }
  ::v-deep .el-dropdown-menu {
    width: 84px;
    height: 66px;
    padding: 6px 12px !important;
    box-sizing: border-box;
    font-size: 14px;
    .el-dropdown-item {
      padding: 0 !important;
    }
  }
  .piemTableBox ::v-deep .el-table__empty-block {
    width: 320px !important;
    height: 320px !important;
    position: absolute;
    top: calc(50% - 160px);
    left: calc(50% - 160px);
    background-image: url("../assets/empty.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 180px;
    span {
      position: relative;
      top: 120px;
    }
  }
}
</style>
