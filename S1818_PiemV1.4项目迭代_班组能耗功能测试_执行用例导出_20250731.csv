标题,执行用例 ID,所属分组,用例状态,执行者,前置条件,步骤描述,预期结果,执行结果,用例类型,用例等级,标签
新建弹窗界面验证,,班组排班方案 | 排班方案 | 新增,已通过,陈树东,![Screenshot-13.png](https://tbfile.cet-electric.com:4753/thumbnail/013fd51d223cb7b5fab72ba15ea995915378/w/200/h/131 "200x131"),"【1】页面空间分配
【2】按钮形状
【3】各部分颜色","【1】与原型一致
【2】与原型一致
【3】与原型一致","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P1,
新增按钮是否有效,,班组排班方案 | 排班方案 | 新增,已通过,陈树东,,【1】点击新增排版方案,【1】弹出新增排版方案弹窗,【1】与预期一致,功能测试,P1,
弹窗属性默认值校验,,班组排班方案 | 排班方案 | 新增,已通过,陈树东,,【1】进入新增排版方案弹窗,"【1】排版方案名称为空
班组类型默认选择第一个班组",【1】与预期一致,功能测试,P1,
排班方案名称长度校验,,班组排班方案 | 排班方案 | 新增,已通过,陈树东,"进入新增排版方案弹窗

名称长度范围为1~20","【1】排版方案名称中输入20个字符
【2】排版方案名称中输入一个字符
【3】排版方案名称中输入21个字符
【4】排版方案名称中不输入字符","【1】保存成功，排版方案的名称为输入的内容
【2】保存成功，排版方案的名称为输入的内容
【3】保存失败，给出字符超出界限的提示
【4】保存失败，给出字符超出界限的提示","【1】与预期一致
【2】与预期一致
【3】只保留前20个字符
【4】与预期一致",功能测试,P1,
排版方案名称任意字符校验,,班组排班方案 | 排班方案 | 新增,已通过,陈树东,进入新增排版方案弹窗,【1】输入数字、特殊字符、中/英文,【1】均支持输入保存,【1】与预期一致,功能测试,P2,
排班方案名称必填项校验,,班组排班方案 | 排班方案 | 新增,已通过,陈树东,,【1】排班方案名称输入为空,【1】不支持，对应保存时有相应的提示信息,【1】与预期一致,功能测试,P3,
排班方案名称唯一性校验-同个项目,,班组排班方案 | 排班方案 | 新增,已通过,陈树东,同个项目,【1】在排版方案名称中输入已存在的名称,【1】不支持，保存时对应会有提示信息,【1】与预期一致,功能测试,P2,
排班方案名称唯一性校验-不同项目,,班组排班方案 | 排班方案 | 新增,待评审,,手动修改项目id,【1】在排版方案名称中输入其他项目已存在的名称,【1】支持,,功能测试,P2,
班组类型来源校验,,班组排班方案 | 排班方案 | 新增,已通过,陈树东,"来自班组类型枚举表（classTeamTypeEnum）

![Screenshot-12.png](https://tbfile.cet-electric.com:4753/thumbnail/013f3a01d2e38d0b68d3128f96dfc40b5410/w/600/h/107 ""600x107"")",【1】进入新增排版方案弹窗,【1】班组类型中的数据与classTeamTypeEnum一致,【1】与预期一致,功能测试,P2,
班组类型顺序验证,,班组排班方案 | 排班方案 | 新增,已通过,陈树东,,【1】进入新增排班方案弹窗,【1】班组类型顺序固定为运维班组、生产班组,【1】与预期一致,功能测试,P2,
班组类型选项方式验证,,班组排班方案 | 排班方案 | 新增,已通过,陈树东,,"【1】单选
【2】多选","【1】支持
【2】不支持","【1】与预期一致
【2】与预期一致",功能测试,P1,
取消新增弹窗,,班组排班方案 | 排班方案 | 新增,已通过,陈树东,,"【1】点击取消按钮
【2】点击新增排班方案按钮","【1】关闭弹窗，编辑的内容不保存，排班方案列表中不新增
【2】弹窗属性内容恢复为默认值","【1】与预期一致
【2】与预期一致",功能测试,P2,
关闭新增弹窗,,班组排班方案 | 排班方案 | 新增,已通过,陈树东,,"【1】点击右上角的关闭图标
【2】重新进入新增排班方案弹窗","【1】关闭弹窗成功，编辑的内容不保存，排班方案列表不新增数据
【2】弹窗中的属性值恢复为默认","【1】与预期一致
【2】与预期一致",功能测试,P2,
新增生产班组,,班组排班方案 | 排班方案 | 新增 | 场景,已通过,陈树东,,"【1】点击新增排版方案按钮
【2】在排班方案名称输入【排版方案1】
【3】选择班组类型为【生产班组】
【4】点击保存
【5】数据库schedulingscheme验证
【6】操作日志验证","【1】弹出新增排版方案弹窗
【2】排版方案输入框显示【排版方案1】
【3】班组类型在生产班组选项上勾选
【4】新增成功，在列表尾部新增的排版方案
【5】在数据库表中存在新增数据，新增数据的classteamtype=2
【6】数据库操作日志eemoperationlog 中存在一条operationtype=1的数据记录

在操作日志界面可以查看新增的记录","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致",功能测试,P0,
新增运维班组,,班组排班方案 | 排班方案 | 新增 | 场景,已通过,陈树东,,"【1】点击新增排版方案按钮
【2】在排班方案名称输入【排版方案1】
【3】选择班组类型为【生产班组】
【4】点击保存
【5】数据库schedulingscheme表验证
【6】操作日志","【1】弹出新增排版方案弹窗
【2】排版方案输入框显示【排版方案1】
【3】班组类型在生产班组选项上勾选
【4】新增成功，在列表尾部新增的排版方案
【5】在数据库中存在新增数据，新增数据的
【6】数据库操作日志eemoperationlog 中存在新增记录classteamtype=1


数据库操作日志eemoperationlog 中存在一条operationtype=1的数据记录

在操作日志界面可以查看新增的记录","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致",功能测试,P0,
编辑按钮是否有效校验,,班组排班方案 | 排班方案 | 编辑,已通过,陈树东,,【1】点击编辑按钮,【1】进入编辑界面,【1】与预期一致,功能测试,P1,
编辑弹窗界面校验,,班组排班方案 | 排班方案 | 编辑,已通过,陈树东,,【1】进入编辑排版方案弹窗,【1】与原型设计图一致,【1】与预期一致,功能测试,P1,
编辑弹窗属性校验,,班组排班方案 | 排班方案 | 编辑,已通过,陈树东,,【1】进入编辑弹窗,【1】排班方案名称与班组类型与排班方案表schedulingscheme中的数据一致,【1】与预期一致,功能测试,P1,
编辑弹窗中属性是否支持编辑,,班组排班方案 | 排班方案 | 编辑,未通过,陈树东,,"【1】班组列表为空
【2】班组列表中存在数据","【1】排版方案名称与班组类型均支持编辑
【2】排班方案名称支持编辑
班组类型不支持编辑","【1】与预期一致
【2】仍然可以编辑",功能测试,P2,
清空排版方案名称,,班组排班方案 | 排班方案 | 编辑,已通过,陈树东,,【1】清空排版方案名称,【1】不支持，保存时对应会有提示信息,【1】与预期一致,功能测试,P1,
排班方案名称修改为重复名称,,班组排班方案 | 排班方案 | 编辑,已通过,陈树东,同个项目,【1】将排班方案名称修改为现在存在的名称,【1】不支持，保存时对应会有提示信息,【1】与预期一致,功能测试,P1,
排班方案名称编辑超过界限,,班组排班方案 | 排班方案 | 编辑,已通过,陈树东,,【1】排班方案名称修改超过20个字符,【1】不支持，保存时会有对应的提示信息,【1】只保留前20个字符,功能测试,P1,
取消修改,,班组排班方案 | 排班方案 | 编辑,已通过,陈树东,,"【1】点击取消按钮
【2】数据库schedulingscheme验证","【1】弹窗关闭，列表中数据不更新
【2】数据库中数据不会更新","【1】与预期一致
【2】与预期一致",功能测试,P1,
关闭编辑弹窗,,班组排班方案 | 排班方案 | 编辑,已通过,陈树东,,"【1】点击弹窗右上角X图标
【2】数据库schedulingscheme验证","【1】关闭弹窗成功，编辑的内容不保存，排班方案列表不更新
【2】数据库数据没有更改","【1】与预期一致
【2】与预期一致",功能测试,P1,
编辑排班方案,,班组排班方案 | 排班方案 | 编辑 | 场景,已通过,陈树东,,"【1】点击编辑按钮
【2】修改排班方案名称为【排班方案2】
【3】修改班组类型为【运维班组】
【4】点击保存
【5】数据库schedulingscheme信息验证
【6】操作日志eemoperationlog 验证","【1】进入编辑排班方案弹窗
【2】排班方案输入框显示为【排版方案2】
【3】运维班组为选中状态
【4】保存成功，列表中排版方案名称与班组类型显示为修改的内容
【5】数据库数据更新为修改后的内容。其中classteamtype=1
【6】数据库中存在编辑的操作记录
数据库中存在operationtype=1的操作记录

在操作日志前端界面中可以看到该记录","【1】与预期一致
【2】与预期一致与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致",功能测试,P0,
删除按钮是否有效,,班组排班方案 | 排班方案 | 删除,已通过,陈树东,,"【1】班组类型为运维班组
点击删除按钮
【2】1、班组类型为生产班组
2、点击三点按钮
3、点击删除按钮","【1】弹出删除确认窗口
【2】弹出删除确认窗口","【1】与预期一致
【2】与预期一致",功能测试,P1,
取消删除,,班组排班方案 | 排班方案 | 删除,已通过,陈树东,点击排班方案删除按钮,"【1】点击取消按钮
【2】数据库schedulingscheme数据验证
【3】操作日志eemoperationlog 验证","【1】取消删除成功，选中的排班方案没有被删除，没有调用接口
【2】数据库中选中数据没有被删除
【3】数据库中没有删除记录","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P1,
关闭删除确认弹窗,,班组排班方案 | 排班方案 | 删除,已通过,陈树东,打开删除确弹窗,"【1】点击右上角关闭图标
【2】数据库schedulingscheme验证
【3】操作日志eemoperationlog 验证","【1】关闭弹窗成功，取消删除操作，排班方案列表中仍然存在选中数据，没有调用删除接口
【2】数据库中选中数据没有被删除
【3】操作日志中没有删除操作的日志记录","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P1,
存在关联关系-点击删除,,班组排班方案 | 排班方案 | 删除,待评审,,,"【1】存在班次方案
【2】存在班组列表
【3】存在节假日配置","【1】支持，删除之后classesscheme中关联的数据也一起删除
【2】支持，删除之后teamgroupinfo中关联的数据
【3】支持，删除之后holidayconfig中关联的数据","【1】null
【2】null
【3】null",功能测试,P1,
删除排班方案-没有班次方案与班组列表,,班组排班方案 | 排班方案 | 删除 | 场景,待评审,,,"【1】选中排班方案
【2】点击删除按钮
【3】点击确认
【4】数据库验证
【5】操作日志验证","【1】排班方案为选中状态
【2】弹出删除确认弹窗
【3】删除成功，删除的排班方案不显示在列表
【4】schedulingscheme中删除该数据记录
【5】eemoperationlog 中存在刚刚的删除记录
数据库中存在operationtype=2的操作记录

在操作日志前端界面中可以看到该记录",,功能测试,P0,
删除排班方案-已存在班次方案,,班组排班方案 | 排班方案 | 删除 | 场景,待评审,,班次方案中存在数据,"【1】选中已有班次方案的排班方案
【2】点击删除按钮
【3】点击确认
【4】数据库验证
【5】操作日志验证","【1】排班方案为选中状态
【2】弹出删除确认弹窗
【3】删除成功，选中的排班方案被删除了。
【4】数据表schedulingscheme中没有删除的数据
数据表classesscheme、classesconfig中数据也被删除
【5】数据表eemoperationlog存在刚刚的删除日志 
数据库中存在operationtype=2的操作记录

在操作日志前端界面中可以看到该记录",,功能测试,P1,
删除排班方案-已存在班组列表,,班组排班方案 | 排班方案 | 删除 | 场景,待评审,,班次方案中存在数据,"【1】选中已有班组列表的排班方案
【2】点击删除按钮
【3】点击确认
【4】数据库验证
【5】操作日志验证","【1】排班方案为选中状态
【2】弹出删除确认弹窗
【3】删除成功，选中的排班方案被删除了。
【4】数据表schedulingscheme中没有删除的数据
数据表teamgroupinfo中数据也被删除
【5】数据表eemoperationlog存在刚刚的删除日志 
数据库中存在operationtype=2的操作记录

在操作日志前端界面中可以看到该记录",,功能测试,P1,
删除排班方案-已有班次方案与班组列表,,班组排班方案 | 排班方案 | 删除 | 场景,待评审,,班次方案与班组列表中存在数据,"【1】选中已有班次方案与班组列表的排班方案
【2】点击删除按钮
【3】点击确认
【4】数据库验证
【5】操作日志验证","【1】排班方案为选中状态
【2】弹出删除确认弹窗
【3】删除成功，选中的排班方案被删除了。
【4】数据表schedulingscheme中没有删除的数据
数据表classesscheme、classesconfig中数据也被删除
数据表teamgroupinfo中数据也被删除
【5】数据表eemoperationlog存在刚刚的删除日志
数据库中存在operationtype=2的操作记录

在操作日志前端界面中可以看到该记录",,功能测试,P0,
关联节点按钮效果校验,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,,【1】点击关联节点按钮,【1】弹窗关联节点弹窗,【1】与预期一致,功能测试,P2,
关联节点弹窗样式验证,,班组排班方案 | 排班方案 | 关联节点,待评审,,,【1】点击关联设备按钮,【1】弹窗与UI原型一致,【1】与预期一致,功能测试,P2,
关联节点弹窗默认值校验,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,,"【1】
【2】
【3】","【1】默认不勾选【默认勾选子节点】
【2】搜索框默认为空
【3】节点树默认全不勾选","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
勾选“默认选中子节点”后，选择节点树,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,勾选默认选中子节点,"【1】勾选某父节点
【2】勾选某父节点下所有子节点
【3】取消勾选某父节点
【4】取消该父节点下所有子节点
【5】取消勾选某父节点下部分子节点","【1】除该父节点外，同时勾选该父节点下所有子节点
【2】该父节点自动勾选
【3】除该父节点外，同时取消勾选该父节点下所有子节点
【4】该父节点自动取消勾选
【5】该父节点取消勾选","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致",功能测试,P2,
节点选择容错处理,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,,"【1】已经选择节点A，勾选“默认选中子节点”
【2】取消勾选“默认选中子节点”","【1】自动勾选所有同子级节点

【2】节点树无变化","【1】与预期一致
【2】与预期一致",功能测试,P3,
搜索框-搜索范围验证,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,,【1】搜索范围验证,【1】针对节点树名称搜索,【1】与预期一致,功能测试,P2,
搜索框-样式确认,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,,【1】搜索框文字验证,【1】搜索框为空时，提示请输入关键字（灰色）,【1】与预期一致,功能测试,P2,
搜索框-搜索内容验证,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,,"【1】搜索框不输入任何字符
【2】输入中文筛选字符
【3】输入英文筛选字符
【4】输入数字筛选字符
【5】输入符号筛选字符
【6】删除部分筛选字符
【7】删除所有筛选字符","【1】显示所有的查询结果
【2】正确筛选列表节点名称，搜索之后的节点树为展开状态
【3】正确筛选列表节点名称，搜索之后的节点树为展开状态
【4】正确筛选列表节点名称，搜索之后的节点树为展开状态
【5】正确筛选列表节点名称，搜索之后的节点树为展开状态
【6】正确筛选列表节点名称，搜索之后的节点树为展开状态
【7】正常加载全部节点信息","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致
【7】与预期一致",功能测试,P2,
节点树数据确认,,班组排班方案 | 排班方案 | 关联节点,待评审,,,"【1】来源
【2】父子节点关系","【1】管理层级节点
select * from project p ;--项目
select * from sectionarea s ;--园区
select * from building b  ;--楼栋
select * from floor f ;--楼层
select * from room r ;--房间
select * from manuequipment m ;--用能设备
【2】select * from model_instance_relationship mir where atype = 'project' and btype = 'sectionarea';--项目-园区
select * from model_instance_relationship mir where atype = 'sectionarea' and btype = 'building';--园区-楼栋
select * from model_instance_relationship mir where atype = 'building' and btype = 'floor';--楼栋-楼层
select * from model_instance_relationship mir where atype = 'floor' and btype = 'room';--楼层-房间
select * from model_instance_relationship mir where atype = 'room' and btype = 'manuequipment';--房间-设备",,功能测试,P2,
节点树默认样式校验,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,,【1】进入关联节点弹窗,【1】节点树默认为展开状态,【1】默认为收缩状态,功能测试,P2,
节点树-完整性验证,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,,【1】完整性,"【1】节点过多时出现上下滚动条
节点名称过长时出现左右滚动条",【1】与预期一致,功能测试,P2,
权限验证,,班组排班方案 | 排班方案 | 关联节点,待评审,,,"【1】登录用户拥有根节点权限
【2】登录用户仅拥有部分节点权限
【3】登录用户无节点权限","【1】全部节点可查看
【2】可查看部分节点
【3】不可看",,功能测试,P0,
节点树是否支持勾选,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,,【1】选中节点树节点,【1】选中节点为勾选状态,【1】与预期一致,功能测试,P2,
编辑关联节点-默认值校验,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,排班方案已配置关联节点,"【1】点击关联节点按钮
【2】查看弹窗属性
【3】数据库校验","【1】打开关联节点弹窗
【2】弹窗默认为展开状态，关联的节点都为勾选状态
【3】勾选节点与schedulingschemetonode表一致","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
取消关联节点,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,,"【1】点击取消按钮
【2】重新进入关联节点
【3】","【1】取消关联成功
编辑的内容不保存
schedulingschemetonode表中不更新数据

【2】之前编辑的内容不保存
【3】","【1】与预期一致
【2】与预期一致",功能测试,P2,
关闭关联节点弹窗,,班组排班方案 | 排班方案 | 关联节点,已通过,陈树东,,"【1】点击右上角关闭图表
【2】重新进入关联节点","【1】关闭弹窗成功
编辑的内容不保存
schedulingschemetonode表中不更新数据

【2】之前编辑的内容不保存","【1】与预期一致
【2】与预期一致",功能测试,P2,
配置关联节点,,班组排班方案 | 排班方案 | 关联节点 | 场景,已通过,陈树东,班组类型为生产班组,"【1】点击关联节点按钮
【2】点击勾选关联节点
【3】点击确定
【4】数据库数据验证
【5】操作日志验证","【1】打开关联节点弹窗
【2】选中节点为勾选状态
【3】保存成功，排班方案成功关联节点
【4】数据表schedulingschemetonode中存在关联节点
【5】数据表eemoperationlog 中存在刚刚的操作日志","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致",功能测试,P0,
不关联节点点击保存,,班组排班方案 | 排班方案 | 关联节点 | 场景,已通过,陈树东,班组类型为生产班组,"【1】点击关联节点
【2】不勾选关联节点
【3】点击确定","【1】打开关联节点弹窗
【2】
【3】保存成功，schedulingschemetonode中没有新增数据，eemoperationlog 中有刚刚编辑的记录","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
筛选之后配置关联节点,,班组排班方案 | 排班方案 | 关联节点 | 场景,已通过,陈树东,,"【1】在搜索框中输入搜索词
【2】勾选默认勾选子节点
【3】勾选某父节点
【4】点击确定","【1】节点树列表中显示只含有搜索词的节点
【2】默认勾选子节点为选中状态
【3】只选中搜索之后的子节点
【4】关联成功，数据表schedulingschemetonode中存在关联的数据，eemoperationlog 中有刚刚编辑的操作记录","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P2,
编辑关联节点,,班组排班方案 | 排班方案 | 关联节点 | 场景,已通过,陈树东,排班方案已关联节点,"【1】点击关联节点按钮
【2】修改关联节点
【3】点击确定","【1】打开关联节点弹窗
【2】关联节点为修改后的节点
【3】修改成功。数据表schedulingschemetonode中的关联节点数据更新为修改后的数据；
操作日志schedulingschemetonode中有刚刚的操作日志","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
列表为空时展示,,班组排班方案 | 排班方案 | 展示,未通过,陈树东,,【1】进入排班方案页面,"【1】排班方案列表中显示列表暂无数据且有图标
班次方案与班组列表中的新增按钮无法点击",【1】班次方案与班组列表中新增按钮可以点击,功能测试,P2,
班组类型为运维班组展示,,班组排班方案 | 排班方案 | 展示,已通过,陈树东,,【1】进入排版方案页面,【1】在节假日后面直接显示删除按钮,【1】节假日本次不开发，其余与预期一致,功能测试,P2,
班组类型为生产班组展示,,班组排班方案 | 排班方案 | 展示,已通过,陈树东,,【1】进入排班方案页面,【1】在节假日配置按钮后面为三点的按钮，点击之后显示关联节点与编辑按钮,【1】与预期一致,功能测试,P2,
页面存在多个排班方案-出现上下滚动条,,班组排班方案 | 排班方案 | 展示,已通过,陈树东,,【1】页面存在多个排班方案,【1】出现上下滚动条,【1】与预期一致,功能测试,P1,
排班方案名称为20个字符展示效果验证,,班组排班方案 | 排班方案 | 展示,已通过,陈树东,,【1】排班方案名称为20个字符,【1】超出部分省略号显示,【1】与预期一致,功能测试,P3,
搜索框样式验证,,班组排班方案 | 排班方案 | 搜索框,已通过,陈树东,,【1】,【1】搜索框样式与文本与原型设计图一致,【1】与预期一致,功能测试,P2,
搜索框是否允许输入,,班组排班方案 | 排班方案 | 搜索框,已通过,陈树东,,【1】输入字符,【1】支持,【1】与预期一致,功能测试,P2,
搜索范围验证,,班组排班方案 | 排班方案 | 搜索框,已通过,陈树东,,【1】,【1】仅针对排班方案名称进行检索,【1】与预期一致,功能测试,P3,
搜索框输入内容校验,,班组排班方案 | 排班方案 | 搜索框,已通过,陈树东,,"【1】输入中文
【2】输入英文
【3】输入数字
【4】输入符号","【1】支持
【2】支持
【3】支持
【4】支持","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P3,
搜索框如何触发查询,,班组排班方案 | 排班方案 | 搜索框,已通过,陈树东,,"【1】点击放大镜的搜索图标
【2】按下回车键
【3】点击空白处
【4】编辑搜索框","【1】触发查询
【2】触发查询
【3】无法触发查询
【4】触发查询","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】无法触发查询",功能测试,P3,
在搜索框中输入有效的搜索词,,班组排班方案 | 排班方案 | 搜索框 | 场景,已通过,陈树东,,【1】在搜索框中输入搜索词,"【1】页面中显示含有搜索词的数据；
默认选择筛选后列表中的第一个排班方案",【1】与预期一致,功能测试,P0,
在搜索框中输入无效的搜索词,,班组排班方案 | 排班方案 | 搜索框 | 场景,阻塞,陈树东,,【1】在搜索框中输入无效的搜索词,【1】排班方案列表显示暂无数据，且包含图标，班次方案与班组列表的新增按钮无法点击,【1】班次方案与班组列表新增按钮可以点击,功能测试,P2,
删除部分搜索字符,,班组排班方案 | 排班方案 | 搜索框 | 场景,已通过,陈树东,,【1】删除部分搜索字符,【1】排班方案列表更新为重新搜索后的数据,【1】与预期一致,功能测试,P2,
删除所有搜索字符,,班组排班方案 | 排班方案 | 搜索框 | 场景,已通过,陈树东,,【1】删除所有搜索字符,【1】排班方案列表中显示所有的数据,【1】与预期一致,功能测试,P2,
搜索之后访问别的标签页后返回,,班组排班方案 | 排班方案 | 搜索框 | 场景,已通过,陈树东,,"【1】在搜索框中输入搜索词
【2】访问别的标签页
【3】点击排班方案页面","【1】排班方案列表中显示含有搜索词的排班方案
【2】跳转到该标签页
【3】重新进入排班方案页面，搜素框中内容默认清空","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
筛选框样式验证,,班组排班方案 | 排班方案 | 筛选框,已通过,陈树东,,【1】,【1】与原型设计图一致，选项为下拉筛选,【1】与预期一致,功能测试,P2,
筛选框默认值验证,,班组排班方案 | 排班方案 | 筛选框,已通过,陈树东,,"【1】进入排班方案页面
【2】","【1】班组类型筛选框默认选择为全部
【2】",【1】与预期一致,功能测试,P2,
筛选框选项确认,,班组排班方案 | 排班方案 | 筛选框,已通过,陈树东,,"【1】选项内容确认
【2】选项顺序确认","【1】有三个选项：全部、运维班组、生产班组
【2】第一个选项为全部
第二个选项为运维班组
第三个选项为生产班组","【1】与预期一致
【2】与预期一致",功能测试,P2,
筛选生产班组,,班组排班方案 | 排班方案 | 筛选框 | 场景,已通过,陈树东,,"【1】点击班组类型筛选框
【2】选中生产班组筛选项","【1】打开下拉筛选项
【2】排班方案列表只显示班组类型为生产班组的排班方案，默认选中筛选之后的第一个排班方案","【1】与预期一致
【2】与预期一致",功能测试,P0,
筛选运维班组,,班组排班方案 | 排班方案 | 筛选框 | 场景,已通过,陈树东,,"【1】点击班组类型筛选框
【2】选中运维班组筛选项","【1】打开下拉筛选项
【2】排班方案列表只显示班组类型为运维班组的排班方案，默认选中筛选后的第一个排班方案","【1】与预期一致
【2】与预期一致",功能测试,P0,
筛选类型为恢复为全部,,班组排班方案 | 排班方案 | 筛选框 | 场景,已通过,陈树东,,"【1】点击筛选框
【2】选中全部","【1】展开下拉筛选项
【2】排班方案列表展示所有的排班方案，默认选中列表中的第一个排班方案","【1】与预期一致
【2】与预期一致",功能测试,P0,
筛选没有的班组类型,,班组排班方案 | 排班方案 | 筛选框 | 场景,阻塞,陈树东,排班方案列表中只存在运维班组类型的排班方案,"【1】点击下拉筛选框
【2】选中生产班组选项","【1】展开下拉筛选项
【2】排班方案列表中展示【列表暂无内容】，班次方案与班组列表新增按钮无法点击","【1】与预期一致
【2】班次方案与班组列表新增按钮可以点击",功能测试,P2,
筛选之后点击访问别的标签页在返回,,班组排班方案 | 排班方案 | 筛选框 | 场景,已通过,陈树东,,"【1】班组类型选择为生产班组
【2】访问用能分析标签页
【3】点击排班方案页面","【1】排班方案列表中只显示生产班组的排班方案
【2】跳转到用能分析页面
【3】返回排班方案页面，选项值恢复为默认","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
新增按钮是否生效,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,【1】点击新增班次方案,【1】打开新增班次弹窗,【1】与预期一致,功能测试,P2,
新增班次方案弹窗样式校验,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,![Screenshot-0.png](https://tbfile.cet-electric.com:4753/thumbnail/013fa288b53d66764fa6d336492eba2ff554/w/200/h/103 "200x103"),【1】点击新增班次方案,【1】弹窗与UI原型设计图一致,【1】与预期一致,功能测试,P2,
班次方案名称必填项校验,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,【1】班次方案名称为空,【1】不支持，保存时会有对应的提示信息,【1】与预期一致,功能测试,P2,
班次方案名称长度验证,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,"【1】在班次方案名称输入20个字符
【2】在班次方案名称输入1个字符
【3】在班次方案名称输入21个字符
【4】在班次方案名称不输入内容","【1】支持，确定时可以正常保存
【2】支持，确定时可以正常保存
【3】不支持，确定时会有正确的提示信息
【4】不支持，确定时会有正确的提示信息","【1】与预期一致
【2】与预期一致
【3】只保留前20个字符
【4】与预期一致",功能测试,P2,
班次方案名称任意字符验证,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,"【1】输入中文
【2】输入特殊字符
【3】输入英文
【4】输入数字
【5】输入空格","【1】支持输入
【2】支持输入
【3】支持输入
【4】支持输入
【5】不支持","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致",功能测试,P2,
班次方案名称唯一性校验,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,在同个排班方案中创建班次方案,【1】输入已存在的班次方案名称,【1】支持，在保存时正常保存,【1】不支持，有提示信息,功能测试,P2,
班次名称必填项校验,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,"【1】班次名称为空
【2】","【1】不支持，保存时会有对应的提示信息
【2】",【1】与预期一致,功能测试,P2,
班次名称长度验证,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,"【1】班次名称中输入20个字符
【2】班次名称中输入1个字符
【3】班次名称中输入21个字符
【4】班次名称中不输入字符","【1】支持，确定保存时可以正常保存
【2】支持，确定保存时可以正常保存
【3】不支持，确定保存时会有合理的提示信息
【4】不支持，确定保存时会有合理的提示信息","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P2,
班次名称任意字符验证,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,"【1】输入中文
【2】输入特殊字符
【3】输入英文
【4】输入数字
【5】输入空格","【1】支持输入
【2】支持输入
【3】支持输入
【4】支持输入
【5】不支持","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致",功能测试,P2,
班次名称唯一性校验-同个班次方案,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,在同个班次方案中,【1】输入已存在的班次名称,【1】不支持，在保存确定时会有提示信息,【1】与预期一致,功能测试,P0,
班次名称唯一性校验-不同班次方案,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,【1】输入其他班次方案中已存在的班次名称,【1】支持，确认保存时可以正常保存,【1】与预期一致,功能测试,P2,
选择时段默认展示是否正确,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,【1】,【1】时间选择器默认为空,【1】与预期一致,功能测试,P2,
选择时段必填项校验,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,"【1】不设置开始时间
【2】不设置结束时间
【3】开始时间与结束时间都不配置","【1】不支持，确定保存时会有合理的提示信息
【2】不支持，确定保存时会有合理的提示信息
【3】不支持，确定保存时会有合理的提示信息","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P3,
选择时段-是否支持手动填写,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,【1】手动输入时间,【1】不支持，无法手动输入时间,【1】与预期一致,功能测试,P2,
选择时段-选择范围验证,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,"【1】点击开始时间选框
【2】点击结束时间选框","【1】开始时间的选择范围为0:00~23:30
【2】结束时间的选择范围为0:00~23:30","【1】与预期一致
【2】与预期一致",功能测试,P2,
选择时段-录入规则验证,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,"【1】开始时间早于结束时间
【2】开始时间等于结束时间
【3】开始时间晚于结束时间","【1】支持
【2】支持
【3】支持，时间范围为跨天数据","【1】与预期一致
【2】不支持，开始时间不可以等于结束时间
【3】与预期一致",功能测试,P2,
选择时段-配置时段范围超过24小时验证,,班组排班方案 | 班次方案 | 新增,未通过,陈树东,,【1】配置的班次时段范围超过24小时,【1】不支持，保存时会有提示信息,【1】超过24小时仍然可以保存,功能测试,P2,
时段重复性校验-同个班次,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,同个班次方案中,"【1】1、配置早班时间为8:00-12:00
2、配置午班的时间为10:00-12:00",【1】不支持，确定保存时会有合理的提示信息,【1】与预期一致,功能测试,P2,
时段重复性校验-不同班次方案,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,不同班次方案中,【1】配置别的班次方案已存在的时段,【1】支持,【1】与预期一致,功能测试,P2,
跨天时段-后端处理逻辑验证,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,开始时间>结束时间,"【1】时段配置为
1:00-8:30
8:30-17:00
17:00-1:00
【2】时段配置为
17:00-1:00
1:00-8:30
8:30-17:00

","【1】传入后端时会识别第二个1:00为后一天数据
【2】传入后端时会识别第一个17:00为前一天数据","【1】与预期一致
【2】与预期一致",功能测试,P1,
添加班次配置,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,"【1】在最后一列配置项中点击添加按钮
【2】在第一列配置后面点击添加按钮","【1】在该班次配置后面新增一列配置项
【2】在第一列配置后面新增一列配置项","【1】与预期一致
【2】与预期一致",功能测试,P2,
删除班次配置,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,"【1】选中班次配置点击删除按钮
【2】","【1】1、删除成功，选中的班次配置被删除
2、其配置的时段、名称可以重新配置
3、不会调用删除接口，数据库classesconfig中不会更新数据
【2】",【1】与预期一致,功能测试,P2,
班次数量上限验证,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,班次数量已有4个,"【1】点击添加班次
【2】删除班次之后点击添加","【1】添加失败，提示班次数量最多为4个
【2】可以正常添加","【1】与预期一致
【2】与预期一致",功能测试,P0,
取消新增,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,"【1】点击取消按钮
【2】重新进入新增班次方案","【1】取消成功，编辑的内容不保存，班次方案列表不新增班次方案
【2】之前编辑的内容不保存","【1】与预期一致
【2】与预期一致",功能测试,P2,
关闭新增班次弹窗,,班组排班方案 | 班次方案 | 新增,已通过,陈树东,,"【1】点击右上角的关闭图表
【2】重新进入新增班次方案弹窗","【1】关闭弹窗成功，编辑的内容不保存
数据库中的classesscheme、classesconfig表中没有新增数据
【2】之前编辑的内容保存","【1】与预期一致
【2】与预期一致",功能测试,P2,
新增班次方案,,班组排班方案 | 班次方案 | 新增 | 场景,已通过,陈树东,,"【1】点击新增班次方案
【2】输入班次方案名称为【四班三倒】
输入班次名称【夜班】
选择时段【0:00-8:00】
【3】点击添加班次按钮
【4】输入班次方案名称为【四班三倒】
输入班次名称【白班】
选择时段【8:00-16:00】
【5】点击添加班次按钮
【6】输入班次方案名称为【四班三倒】
输入班次名称【中班】
选择时段【16:00-24:00】
【7】点击确定","【1】打开新增班次方案弹窗
【2】
【3】
【4】
【5】
【6】
【7】新增成功，在班次方案列表尾部新增的班次方案
数据库中classesscheme、classesconfig中存在新增的数据，且model_instance_relationship表中新增一条关联关系的数据


操作日志eemoperationlog 中存在刚刚的新增记录，新增记录的operationtype无误且在操作日志前端界面可以查看该记录
","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致
【7】与预期一致",功能测试,P0,
新增班次方案-跨天时间为上一天,,班组排班方案 | 班次方案 | 新增 | 场景,已通过,陈树东,,"【1】点击新增班次方案
【2】输入班次方案名称为【四班三倒】
输入班次名称【中班】
选择时段【17:00-1:00】
【3】点击添加班次按钮
【4】输入班次方案名称为【四班三倒】
输入班次名称【夜班】
选择时段【1:00-8:30】
【5】点击添加班次按钮
【6】输入班次方案名称为【四班三倒】
输入班次名称【白班】
选择时段【8:30-17:00】
【7】点击确定","【1】打开新增班次方案弹窗
【2】
【3】
【4】
【5】
【6】
【7】新增成功，在班次方案列表尾部新增的班次方案


数据库中classesscheme、classesconfig中存在新增的数据且model_instance_relationship表中有关联信息


操作日志eemoperationlog 中存在刚刚的新增记录，新增记录的operationtype无误且在操作日志前端界面可以查看该记录
","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致
【7】与预期一致",功能测试,P0,
新增班次方案-跨天时间为下一天,,班组排班方案 | 班次方案 | 新增 | 场景,已通过,陈树东,,"【1】点击新增班次方案
【2】输入班次方案名称为【四班三倒】
输入班次名称【夜班】
选择时段【1:00-8:30】
【3】点击添加班次按钮
【4】输入班次方案名称为【四班三倒】
输入班次名称【白班】
选择时段【8:30-17:00】
【5】点击添加班次按钮
【6】输入班次方案名称为【四班三倒】
输入班次名称【中班】
选择时段【17:00-1:00】
【7】点击确定","【1】打开新增班次方案弹窗
【2】
【3】
【4】
【5】
【6】
【7】新增成功，在班次方案列表尾部新增的班次方案


数据库中classesscheme、classesconfig中存在新增的数据且model_instance_relationship表中有关联信息


操作日志eemoperationlog 中存在刚刚的新增记录，新增记录的operationtype无误且在操作日志前端界面可以查看该记录
","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致
【7】与预期一致",功能测试,P0,
编辑按钮效果验证,,班组排班方案 | 班次方案 | 编辑,已通过,陈树东,,【1】选中班次方案点击编辑按钮,【1】打开编辑班次方案弹窗,【1】与预期一致,功能测试,P3,
编辑弹窗样式验证,,班组排班方案 | 班次方案 | 编辑,已通过,陈树东,,【1】选中班次方案点击编辑按钮,【1】编辑弹窗与原型图一致,【1】与预期一致,功能测试,P2,
编辑班次方案-属性内容验证,,班组排班方案 | 班次方案 | 编辑,已通过,陈树东,,"【1】选中班次方案点击编辑按钮
【2】","【1】班次方案名称与数据库classesscheme一致
班次名称、选择时段为配置的内容与数据库classesconfig一致

【2】",【1】与预期一致,功能测试,P0,
编辑班次方案名称-异常,,班组排班方案 | 班次方案 | 编辑,已通过,陈树东,,"【1】1、打开编辑班次方案弹窗
2、清空班次方案名称
3、点击确定
【2】1、打开编辑班次方案弹窗
2、班次方案名称修改为21个字符
3、点击确定","【1】不支持，保存时会有保存失败的提示信息
【2】不支持，保存时会有保存失败的提示信息","【1】与预期一致
【2】只保留前20个字符输入",功能测试,P2,
编辑班次名称-异常,,班组排班方案 | 班次方案 | 编辑,已通过,陈树东,,"【1】1、打开编辑班次方案弹窗
2、清空班次名称
3、点击确定
【2】1、打开编辑班次方案弹窗
2、班次名称修改为21个字符
3、点击确定","【1】不支持，保存时会有保存失败的提示信息
【2】不支持，保存时会有保存失败的提示信息","【1】与预期一致
【2】只保留前20个字符输入",功能测试,P2,
编辑班次名称-重复性校验,,班组排班方案 | 班次方案 | 编辑,已通过,陈树东,,"【1】1、打开编辑班次弹窗
2、编辑班次名称与其他班次名称一致
3、点击确定",【1】不支持，保存时会有保存失败的提示信息,【1】与预期一致,功能测试,P2,
编辑选择时段-异常,,班组排班方案 | 班次方案 | 编辑,已通过,陈树东,,"【1】1、打开编辑班次方案弹窗
2、清空开始时间
3、点击确定
【2】1、打开编辑班次方案弹窗
2、清空结束时间
3、点击确定","【1】保存失败，提示开始时间不可为空
【2】保存失败，提示结束时间不可为空","【1】与预期一致
【2】与预期一致",功能测试,P2,
编辑班次方案-班次数量上限验证,,班组排班方案 | 班次方案 | 编辑,已通过,陈树东,班次数量为4个,"【1】选中班次方案点击编辑按钮
【2】点击添加按钮","【1】打开编辑班次方案弹窗
【2】不支持，会有合理的提示信息","【1】与预期一致
【2】与预期一致",功能测试,P2,
编辑班次方案-编辑时段存在重复时段,,班组排班方案 | 班次方案 | 编辑,已通过,陈树东,"班次时段中为

1:00-8:30

8:30-17:00

17:00-1:00",【1】编辑班次方案存在重复时段,【1】不支持，保存时会有合适的提示信息,【1】与预期一致,功能测试,P2,
编辑班次方案-删除班次时段配置,,班组排班方案 | 班次方案 | 编辑,已通过,陈树东,,【1】选中班次时段配置点击删除,"【1】编辑班次方案弹窗中不存在删除的班次
被删除的班次名称与时段可以重新配置",【1】与预期一致,功能测试,P2,
取消编辑,,班组排班方案 | 班次方案 | 编辑,已通过,陈树东,,"【1】点击取消编辑按钮
【2】重新进入编辑班次方案弹窗","【1】取消编辑成功，编辑的内容不保存，班次方案列表信息没有变化
数据表classesscheme、classesconfig数据没有变化
数据表eemoperationlog 没有该操作日志

【2】之前编辑的内容不保存","【1】与预期一致
【2】与预期一致",功能测试,P2,
关闭编辑弹窗,,班组排班方案 | 班次方案 | 编辑,已通过,陈树东,,"【1】点击取消编辑按钮
【2】重新进入编辑班次方案弹窗","【1】取消编辑成功，编辑的内容不保存，班次方案列表信息没有变化
数据表classesscheme、classesconfig数据没有变化
数据表eemoperationlog 没有该操作日志

【2】之前编辑的内容不保存","【1】与预期一致
【2】与预期一致",功能测试,P2,
编辑班次方案,,班组排班方案 | 班次方案 | 编辑 | 场景,已通过,陈树东,,"【1】选中班次方案点击编辑按钮
【2】修改班次方案名称
【3】修改班次名称
【4】修改选择时段
【5】点击确定","【1】打开编辑班次方案弹窗
【2】
【3】
【4】
【5】保存成功，班次方案列表中信息更新为修改后的信息


数据表classesscheme、classesconfig更新为修改后的数据


数据表eemoperationlog 中存在编辑操作记录，新增记录的operationtype无误且在操作日志前端界面可以查看该记录","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致",功能测试,P0,
编辑班次方案-添加班次时段配置,,班组排班方案 | 班次方案 | 编辑 | 场景,已通过,陈树东,*班次时段少于4个*,"【1】点击编辑班次方案按钮
【2】点击添加班次时段
【3】配置班次名称与选择时段
【4】点击确定","【1】打开班次方案弹窗
【2】在选中班次时段配置项下方新增一列配置项
【3】
【4】编辑成功，在班次方案列表中班次方案新增了添加的班次时段
数据表classesscheme、classesconfig更新为修改后的数据
数据表eemoperationlog 中存在编辑操作记录，新增记录的operationtype无误且在操作日志前端界面可以查看该记录
","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P2,
编辑班次方案-删除班次时段配置,,班组排班方案 | 班次方案 | 编辑 | 场景,已通过,陈树东,*班次时段少于4个*,"【1】点击编辑班次方案按钮
【2】选中班次点击删除
【3】点击确定","【1】打开班次方案弹窗
【2】选中的班次被删除了
【3】编辑成功，在班次方案列表中班次方案新增了添加的班次时段
数据表classesscheme、classesconfig更新为修改后的数据
数据表eemoperationlog 中存在编辑操作记录，新增记录的operationtype无误且在操作日志前端界面可以查看该记录
","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
删除按钮有效性校验,,班组排班方案 | 班次方案 | 删除,已通过,陈树东,,【1】点击删除按钮,【1】弹出删除班次方案确认弹窗,【1】与预期一致,功能测试,P1,
取消删除,,班组排班方案 | 班次方案 | 删除,已通过,陈树东,,"【1】打开删除弹窗
【2】点击取消按钮","【1】
【2】取消删除成功，选中的班次方案没有被删除
数据表中classesscheme与classesconfig不清除对应的表数据
数据表eemoperationlog 中不存在删除操作记录","【1】与预期一致
【2】与预期一致",功能测试,P1,
关闭删除弹窗,,班组排班方案 | 班次方案 | 删除,已通过,陈树东,,"【1】打开删除弹窗
【2】点击右上角关闭图标","【1】
【2】取消删除成功，选中的班次方案没有被删除
数据表中classesscheme与classesconfig不清除对应的表数据
数据表eemoperationlog 中不存在删除操作记录","【1】与预期一致
【2】与预期一致",功能测试,P1,
删除班次方案,,班组排班方案 | 班次方案 | 删除,待评审,,,"【1】选中班次方案点击删除按钮
【2】点击确定按钮","【1】弹出删除确认弹窗
【2】删除成功，班次方案列表不显示删除的班次方案

数据表中classesscheme与classesconfig清除对应的表数据且model_instance_relationship表中删除关联关系


数据表eemoperationlog 中存在删除操作记录，新增记录的operationtype无误且在操作日志前端界面可以查看该记录","【1】与预期一致
【2】与预期一致",功能测试,P0,
存在关联关系-删除限制校验,,班组排班方案 | 班次方案 | 删除,已通过,陈树东,,【1】班次方案下存在班次配置,【1】支持，删除之后classesscheme模型关联下的classesconfig数据也会被同步删除,【1】与预期一致,功能测试,P1,
班次方案为空时，界面展示,,班组排班方案 | 班次方案 | 查看,已通过,陈树东,,【1】班次方案为空时查看班次方案,【1】显示列表暂无内容与图片,【1】与预期一致,功能测试,P0,
班次方案信息展示,,班组排班方案 | 班次方案 | 查看,已通过,陈树东,,【1】进入班次方案标签页,"【1】页面展示所有的班次方案；每个班次方案展示【班次方案名称、班次名称以及班次时段】与数据表中classesscheme与classesconfig的表数据一致
",【1】与预期一致,功能测试,P0,
班次方案数量过长时-出现上下滚动条,,班组排班方案 | 班次方案 | 查看,已通过,陈树东,班次方案数量过多时,"【1】访问班次方案标签页
【2】","【1】页面出现上下滚动条
【2】",【1】与预期一致,功能测试,P2,
班次方案名称为20个字符展示效果验证,,班组排班方案 | 班次方案 | 查看,已通过,陈树东,班次方案名称为20个字符,【1】,【1】超出部分省略号显示,【1】20个字符正常显示,功能测试,P3,
班组名称字符校验,,班组排班方案 | 班组列表 | 新增,已通过,陈树东,,"【1】1、班组类型为运维班组
2、在班组名称中输入（数字、字符、中/英文/特殊字符）
3、点击保存
【2】1、班组类型为生产班组
2、在班组名称中输入（数字、字符、中/英文/特殊字符）
3、点击保存","【1】保存成功，班组名称为输入的字符
【2】保存成功，班组名称为输入的字符","【1】与预期一致
【2】与预期一致",功能测试,P1,
班组名称长度校验,,班组排班方案 | 班组列表 | 新增,已通过,陈树东,,"【1】输入程序允许的最大长度
【2】输入程序允许的最小长度
【3】输入程序允许的最大长度+1
【4】输入程序允许的最小长度-1","【1】保存成功
【2】保存成功
【3】保存失败，会有合理的提示
【4】保存失败，会有合理的提示","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P1,
关闭弹窗,,班组排班方案 | 班组列表 | 新增,已通过,陈树东,,【1】1、点击右上角的关闭图标,【1】关闭弹窗，编辑内容不保存，不新增班组,【1】与预期一致,功能测试,P2,
班组名称重复性校验,,班组排班方案 | 班组列表 | 新增,已通过,陈树东,,"【1】1、班组名称中输入已存在的班组名称
2、点击确定",【1】保存成功，班组名称可以重复,【1】与预期一致,功能测试,P1,
班组方案数量校验,,班组排班方案 | 班组列表 | 新增,已通过,陈树东,班组数量已经达到10个,"【1】1、点击新增按钮
【2】删除一个班组方案","【1】新增按钮无法点击
【2】可以重新新增班组","【1】与预期一致
【2】与预期一致",功能测试,P0,
新增按钮有效校验,,班组排班方案 | 班组列表 | 新增 | 生产班组,已通过,陈树东,班组类型为生产班组,【1】点击新增班组按钮,【1】弹出新增生产班组弹窗,【1】与预期一致,功能测试,P2,
新增弹窗样式校验,,班组排班方案 | 班组列表 | 新增 | 生产班组,已通过,陈树东,,【1】打开新增班组弹窗,【1】弹窗样式与UI原型图一致,【1】与预期一致,功能测试,P2,
输入框默认值验证-生产班组,,班组排班方案 | 班组列表 | 新增 | 生产班组,已通过,陈树东,,【1】进入新建班组弹窗,"【1】班组名称默认值为空
班组人数默认值为空",【1】与预期一致,功能测试,P2,
班组名称-必填项验证,,班组排班方案 | 班组列表 | 新增 | 生产班组,已通过,陈树东,进入新增班组弹窗界面,【1】班组名称为空点击确定,【1】不支持，确定保存时会有合适的提示信息,【1】与预期一致,功能测试,P2,
班组名称-长度值值校验,,班组排班方案 | 班组列表 | 新增 | 生产班组,已通过,陈树东,进入新增班组弹窗界面,"【1】班组名称中输入20个字符
【2】班组名称中输入1个字符
【3】班组名称中输入21个字符
【4】班组名称中不输入内容
【5】","【1】支持
【2】支持
【3】不支持，确认保存时会有提示信息
【4】不支持，确认保存时会有提示信息
【5】","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P2,
班组名称-唯一性校验,,班组排班方案 | 班组列表 | 新增 | 生产班组,已通过,陈树东,在同个排班方案中创建班组,【1】输入已存在的班组名称,【1】支持，在保存时正常保存,【1】与预期一致,功能测试,P2,
班组名称-任意字符验证,,班组排班方案 | 班组列表 | 新增 | 生产班组,已通过,陈树东,,"【1】输入中文
【2】输入特殊字符
【3】输入英文
【4】输入数字
【5】输入空格","【1】支持输入
【2】支持输入
【3】支持输入
【4】支持输入
【5】不支持","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致",功能测试,P2,
班组人数输入类型校验,,班组排班方案 | 班组列表 | 新增 | 生产班组,未通过,陈树东,班组类型为生产班组,"【1】输入为汉字、特殊字符、英文
【2】负数
【3】小数
【4】正整数、0、不输入","【1】不支持
【2】不支持
【3】不支持
【4】支持","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】输入0时后端数据库不会保存",功能测试,P2,
取消新增,,班组排班方案 | 班组列表 | 新增 | 生产班组,已通过,陈树东,,【1】点击取消按钮,"【1】取消新增成功，班组列表中没有新增数据
数据表teamgroupinfo没有新增数据
数据表eemoperationlog 没有新增操作记录",【1】与预期一致,功能测试,P2,
关闭新增弹窗,,班组排班方案 | 班组列表 | 新增 | 生产班组,已通过,陈树东,,【1】点击右上角的关闭图标,"【1】取消新增成功，班组列表中没有新增数据
数据表teamgroupinfo没有新增数据
数据表eemoperationlog 没有新增操作记录",【1】与预期一致,功能测试,P2,
新增生产班组,,班组排班方案 | 班组列表 | 新增 | 生产班组 | 场景,已通过,陈树东,排班方案中班组类型为生产班组,"【1】点击新增班组按钮
【2】输入班组名称为【1#生产一班】
【3】输入班组人数为12
【4】点击确定","【1】打开新增班组弹窗
【2】
【3】
【4】新增成功，在班组列表尾部新增班组
teamgroupinfo表中新增一条数据，其中name与personcount内容与班组名称与班组人数一致
eemoperationlog 表中新增一条新增班组的操作记录
","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P0,
新增按钮有效性校验,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,排班方案班组类型为运维班组,【1】点击新增班组按钮,【1】打开新增运维班组弹窗,【1】与预期一致,功能测试,P2,
新增弹窗界面校验,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,,【1】打开新增运维班组弹窗,【1】新增弹窗与uI原型设计图一致,【1】与预期一致,功能测试,P2,
输入框默认值校验,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,,【1】进入新增班组弹窗,"【1】班组名称默认值为空
班组成员默认为未勾选状态",【1】与预期一致,功能测试,P1,
班组名称-必填项验证,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,进入新增班组弹窗界面,【1】班组名称为空点击确定,【1】不支持，确定保存时会有合适的提示信息,【1】与预期一致,功能测试,P2,
班组名称-长度值值校验,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,进入新增班组弹窗界面,"【1】班组名称中输入20个字符
【2】班组名称中输入1个字符
【3】班组名称中输入21个字符
【4】班组名称中不输入内容","【1】支持
【2】支持
【3】不支持，确认保存时会有提示信息
【4】不支持，确认保存时会有提示信息","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P2,
班组名称-任意字符验证,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,,"【1】输入中文
【2】输入特殊字符
【3】输入英文
【4】输入数字
【5】输入空格","【1】支持输入
【2】支持输入
【3】支持输入
【4】支持输入
【5】不支持","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致",功能测试,P2,
班组名称-唯一性校验,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,在同个排班方案中创建班组,【1】输入已存在的班组名称,【1】支持，在保存时正常保存,【1】与预期一致,功能测试,P2,
班组成员-必填性校验,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,,【1】班组成员为空,【1】不支持，确认保存时会有合理的提示信息,【1】与预期一致,功能测试,P2,
选择成员-节点树加载是否正确,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,,"【1】来源
【2】父子节点关系","【1】所有用户节点
select * from auth_usergroup ;--用户组列表
select * from auth_user ;--用户列表
【2】select * from auth_usergroup_user_map ;--用户组和用户的关系","【1】与预期一致
【2】与预期一致",功能测试,P2,
班组成员-展示顺序校验,,班组排班方案 | 班组列表 | 新增 | 运维班组,待评审,,,【1】进入新增运维班组弹窗,【1】班组成员的展示顺序与用户列表中的顺序一致,,功能测试,P2,
选择成员-统计是否正确,,班组排班方案 | 班组列表 | 新增 | 运维班组,未通过,陈树东,,"【1】统计数位置
【2】统计数据
【3】数据显示是否完整","【1】同UI，框的右上角
【2】统计选择数据/统计所有数据
【3】显示完整","【1】没有统计数
【2】null
【3】null",功能测试,P3,
选择成员-是否存在滚动条,,班组排班方案 | 班组列表 | 新增 | 运维班组,未通过,,,"【1】上下
【2】左右","【1】存在，超出部分滚动条显示
【2】存在，超出部分滚动条显示","【1】与预期一致
【2】不会出现滚动条，配置页面会错位",功能测试,P3,
选择成员-节点树是否支持模糊搜索,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,,"【1】搜索框位置
【2】输入包含字符
【3】清空字符","【1】同UI，节点树上方
【2】节点树只展示包含字符的数据
【3】全部节点树","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P3,
选择成员-勾选节点,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,,"【1】勾选节点
【2】取消勾选部分节点
【3】取消勾选全部","【1】选中节点为勾选状态；
顶上标题旁的总勾选按钮显示""-""；
统计数据显示勾选的节点数量
【2】取消节点为未勾选状态，顶上标题旁的总勾选按钮仍显示“-”；
统计数量更新为勾选的节点数

【3】全部节点为未勾选；
顶上标题旁的总勾选按钮恢复未勾选状态；
统计数量显示为0","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
选择成员-添加成员为已选成员,,班组排班方案 | 班组列表 | 新增 | 运维班组,未通过,陈树东,,"【1】勾选单个成员
【2】勾选多个成员
【3】勾选全部成员","【1】支持。右侧已选成员框中同步显示勾选的成员，已选成员中的列表无法勾选
左侧框的统计数据也统计正确
【2】支持。右侧已选成员框中同步显示勾选的成员，已选成员中的列表无法勾选
左侧框的统计数据也统计正确
【3】支持。右侧已选成员框中同步显示勾选的成员，已选成员中的列表无法勾选
左侧框的统计数据也统计正确","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P3,
已选成员-节点树加载是否正确,,班组排班方案 | 班组列表 | 新增 | 运维班组,未通过,陈树东,,"【1】来源
【2】父子节点关系","【1】从左侧添加的节点
【2】无节点树，不以节点树形式，直接罗列","【1】与预期一致
【2】当管理组全部选择之后，会出现节点树",功能测试,P2,
已选成员-统计是否正确,,班组排班方案 | 班组列表 | 新增 | 运维班组,阻塞,陈树东,,"【1】统计数位置
【2】统计数据
【3】数据显示是否完整","【1】同UI，框的右上角
【2】展示总数
【3】显示完整",,功能测试,P2,
已选成员-是否存在滚动条,,班组排班方案 | 班组列表 | 新增 | 运维班组,阻塞,陈树东,,"【1】上下
【2】左右","【1】支持，超出后存在滚动条展示
【2】支持，超出后存在滚动条展示","【1】与预期一致
【2】超出时会出现弹窗变形",功能测试,P3,
已选成员-节点是否支持模糊搜索,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,,"【1】搜索框位置
【2】输入包含字符
【3】清空字符","【1】同UI，成员列表上方
【2】只显示包含字符的成员
【3】显示全部成员","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
已选成员-选择成员展示是否正确,,班组排班方案 | 班组列表 | 新增 | 运维班组,阻塞,陈树东,,"【1】勾选成员
【2】取消勾选部分节点
【3】取消勾选全部节点","【1】勾选成员为选中状态
顶上标题旁的总勾选按钮显示""-""
右上角的选中数量显示正确
【2】取消的节点部分为未选中状态
顶上标题旁的总勾选按钮显示""-""
右上角的选中数量显示正确
【3】成员列表全部为未选中状态
顶上标题旁的总勾选按钮显示为勾选状态
右上角的选中数量显示为0",,功能测试,P3,
已选成员-移除成员,,班组排班方案 | 班组列表 | 新增 | 运维班组,阻塞,陈树东,,"【1】取消勾选左侧单个成员
【2】取消勾选左侧多个成员","【1】支持，右侧成员列表中的数据也同步清除，右上角的统计数据也更新

【2】支持，右侧成员列表中的数据也同步清除，右上角的统计数据也更新
",,功能测试,P2,
取消新增,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,,【1】取消按钮,【1】关闭弹窗，编辑内容不保存，不新增班组,【1】与预期一致,功能测试,P1,
关闭新增弹窗,,班组排班方案 | 班组列表 | 新增 | 运维班组,已通过,陈树东,,【1】点击右上角的关闭图表,【1】关闭弹窗，编辑的内容不保存,【1】与预期一致,功能测试,P1,
新增运维班组,,班组排班方案 | 班组列表 | 新增 | 运维班组 | 场景,已通过,陈树东,,"【1】选中班组类型为运维班组的排班方案
【2】点击新增班组按钮
【3】输入班组名称
【4】配置班组成员
【5】点击确定
【6】","【1】
【2】打开新增运维班组弹窗
【3】
【4】
【5】新增班组成功，班组列表尾部新增班组
teamgroupinfo表中新增一条数据，其中name与personid内容与班组名称与班组人数一致
eemoperationlog 表中新增一条新增班组的操作记录
【6】","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致",功能测试,P0,
编辑按钮有效验证,,班组排班方案 | 班组列表 | 编辑,已通过,陈树东,,【1】1、点击编辑按钮,【1】弹出班组编辑弹窗,【1】与预期一致,功能测试,P1,
编辑信息默认值校验,,班组排班方案 | 班组列表 | 编辑,已通过,陈树东,,"【1】打开生产班组编辑弹窗
【2】打开运维班组编辑弹窗","【1】班组名称与班组人数显示为保存的信息
【2】班组名称与班组成员显示为保存的信息","【1】与预期一致
【2】与预期一致",功能测试,P0,
编辑班组名称-异常,,班组排班方案 | 班组列表 | 编辑,已通过,陈树东,,"【1】1、打开编辑生产班组弹窗
2、清空班组名称
3、点击确定
【2】1、打开编辑生产班组弹窗
2、编辑班组名称为21字符
3、点击确定
【3】1、打开编辑运维班组弹窗
2、清空班组名称
3、点击确定
【4】1、打开编辑运维班组弹窗
2、编辑班组名称为21字符
3、点击确定","【1】保存失败，提示班组名称不可为空
【2】保存失败，提示班组名称长度超出界限
【3】保存失败，提示班组名称不可为空
【4】保存失败，提示班组名称长度超出界限","【1】与预期一致
【2】只保留前20个字符
【3】与预期一致
【4】只保留前20个字符",功能测试,P1,
编辑班组人数-异常,,班组排班方案 | 班组列表 | 编辑,未通过,陈树东,,"【1】清空班组人数
【2】班组人数修改为0","【1】支持，保存确定时班组列表中的班组人数显示为--
【2】支持，保存确定时班组列表中的班组人数显示为0","【1】与预期一致
【2】人数显示为--",功能测试,P1,
编辑班组成员,,班组排班方案 | 班组列表 | 编辑,已通过,陈树东,,"【1】1、打开编辑运维班组弹窗
2、添加成员
3、点击确定
【2】1、打开编辑运维班组弹窗
2、移除成员
3、点击确定","【1】班组列表中的班组成员显示新增的成员
【2】班组列表中的班组成员不显示移除的成员","【1】与预期一致
【2】与预期一致",功能测试,P1,
编辑班组成员-异常,,班组排班方案 | 班组列表 | 编辑,已通过,陈树东,,"【1】1、打开运维班组编辑弹窗
2、移除所有成员
3、点击确定",【1】保存失败，提示班组成员不可为空,【1】与预期一致,功能测试,P1,
编辑班组-生产班组,,班组排班方案 | 班组列表 | 编辑,已通过,陈树东,,"【1】点击编辑按钮
【2】编辑班组名称
【3】编辑班组人数
【4】点击保存
【5】数据库信息验证","【1】打开编辑弹窗
【2】班组名称显示为编辑后的内容
【3】班组人数显示为编辑后的内容
【4】编辑成功，班组列表中的班组名称与班组人数显示为编辑后的信息
【5】班组信息表teamgroupinfo中的数据更新为编辑后的数据

操作日子表eemoperationlog 中新增编辑操作记录","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致",功能测试,P0,
编辑班组-运维班组,,班组排班方案 | 班组列表 | 编辑,已通过,陈树东,,"【1】选中班组类型为运维班组的排班方案
【2】点击编辑班组按钮
【3】编辑班组名称
【4】编辑班组成员
【5】点击确定
【6】数据库数据验证","【1】
【2】打开编辑班组弹窗
【3】
【4】
【5】编辑成功，班组列表中的信息更新为编辑后的信息
【6】班组信息表teamgroupinfo中name和personid数据更新为编辑后的数据
操作日志表eemoperationlog中存在编辑操作记录","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致",功能测试,P0,
取消编辑,,班组排班方案 | 班组列表 | 编辑,已通过,陈树东,,【1】点击取消按钮,【1】关闭编辑弹窗。编辑的内容不保存，班组列表中的信息不变化,【1】与预期一致,功能测试,P2,
关闭编辑弹窗,,班组排班方案 | 班组列表 | 编辑,已通过,陈树东,,【1】点击右上角的关闭弹窗,【1】关闭弹窗。编辑的内容不保存，班组列表中的班组信息不变化,【1】与预期一致,功能测试,P2,
删除按钮有效校验,,班组排班方案 | 班组列表 | 删除,已通过,陈树东,,【1】点击删除按钮,【1】弹出删除确认弹窗,【1】与预期一致,功能测试,P1,
删除班组,,班组排班方案 | 班组列表 | 删除,已通过,陈树东,,"【1】1、单击删除按钮
2、点击确定按钮","【1】删除成功，班组列表中不显示删除的班组
班组信息表teamgroupinfo中不存在刚刚删除的数据记录
操作日志表eemoperationlog 中新增删除操作记录日志",【1】与预期一致,功能测试,P0,
取消删除,,班组排班方案 | 班组列表 | 删除,已通过,陈树东,,"【1】1、点击删除按钮
2、点击取消按钮",【1】取消删除，班组列表中仍然存在该班组,【1】与预期一致,功能测试,P2,
班组列表中为空,,班组排班方案 | 班组列表 | 查看,已通过,陈树东,,【1】查看班组列表,【1】页面显示列表暂无数据,【1】与预期一致,功能测试,P0,
班组列表中存在多个班组时会出现滚动条,,班组排班方案 | 班组列表 | 查看,已通过,陈树东,班组列表中存在10个班组,【1】,【1】出现上下滚动条,【1】与预期一致,功能测试,P1,
班组类型为生产班组,,班组排班方案 | 班组列表 | 查看,已通过,陈树东,,【1】进入班组列表,【1】班组列表中每个班组展示班组标签、班组名称、班组人数,【1】与预期一致,功能测试,P0,
班组类型为运维班组,,班组排班方案 | 班组列表 | 查看,已通过,陈树东,,【1】进入班组列表,【1】班组列表中每个班组展示班组标签、班组名称、班组成员,【1】与预期一致,功能测试,P0,
班组成员过多时换行展示,,班组排班方案 | 班组列表 | 查看,已通过,陈树东,,【1】班组成员过多时,【1】班组成员过多时换行展示,【1】与预期一致,功能测试,P1,
班组成员被删除时，已配置的班组会同步清除,,班组排班方案 | 班组列表 | 查看,已通过,陈树东,,"【1】将用户管理中的用户删除
【2】查看运维班组编辑界面","【1】
【2】显示界面中已删除的成员不显示，进入编辑界面时没有该成员","【1】
【2】与预期一致",功能测试,P1,
样式确认,,班组排班方案 | 排班表配置 | 时间框,已通过,陈树东,,【1】,【1】与UI原型图一致,【1】与预期一致,功能测试,P2,
默认值校验,,班组排班方案 | 排班表配置 | 时间框,已通过,陈树东,,【1】进入排班表页面,【1】时间框默认选择为当前月份,【1】与预期一致,功能测试,P0,
筛选范围校验,,班组排班方案 | 排班表配置 | 时间框,已通过,陈树东,,"【1】选择为过去月份
【2】选择为现在月份
【3】选择为未来月份","【1】支持
【2】支持
【3】支持","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
录入方式验证,,班组排班方案 | 排班表配置 | 时间框,已通过,陈树东,,"【1】手动录入
【2】时间选择器","【1】不支持
【2】支持","【1】与预期一致
【2】与预期一致",功能测试,P2,
快捷返回当前月份,,班组排班方案 | 排班表配置 | 时间框,已通过,陈树东,,【1】点击快捷返回当前月份,【1】月份跳转回当前月份,【1】与预期一致,功能测试,P0,
样式验证,,班组排班方案 | 排班表配置 | 排班方案筛选框,已通过,陈树东,,【1】,【1】与UI原型图一致，选项为下拉筛选,【1】与预期一致,功能测试,P2,
数据来源验证,,班组排班方案 | 排班表配置 | 排班方案筛选框,已通过,陈树东,,【1】,【1】数据来源于schedulingscheme表中的数据,【1】与预期一致,功能测试,P2,
数据顺序验证,,班组排班方案 | 排班表配置 | 排班方案筛选框,已通过,陈树东,,【1】,【1】数据顺序与排班方案列表中的顺序一致,【1】与预期一致,功能测试,P2,
默认值验证,,班组排班方案 | 排班表配置 | 排班方案筛选框,已通过,陈树东,,【1】,【1】默认选中第一个排班方案,【1】与预期一致,功能测试,P2,
排班方案名称最长-展示效果验证,,班组排班方案 | 排班表配置 | 排班方案筛选框,已通过,陈树东,排班方案名称为20个字符,"【1】点击下拉选项
【2】选中该排班方案","【1】下拉选项中超出部分会省略显示
【2】在筛选框中超出部分会省略号显示","【1】与预期一致
【2】超出框部分不显示",功能测试,P2,
编辑按钮权限验证,,班组排班方案 | 排班表配置 | 编辑,待评审,,,"【1】登录用户有编辑权限
【2】登录用户没有编辑","【1】页面存在编辑按钮
【2】页面没有编辑按钮",,功能测试,P0,
编辑按钮效果校验,,班组排班方案 | 排班表配置 | 编辑,已通过,陈树东,,【1】点击编辑按钮,"【1】排班表为编辑状态
排班方案、时间框无法切换",【1】与预期一致,功能测试,P1,
一键清空班次,,班组排班方案 | 排班表配置 | 编辑,待评审,,,"【1】点击一键清空班次按钮
【2】点击确定","【1】弹出一键清空班次确认按钮
【2】确定清空成功
排班表中的班次与班次全部清空",【1】,功能测试,P0,
班次方案-数据来源验证,,班组排班方案 | 排班表配置 | 编辑 | 配置班次方案,已通过,陈树东,,【1】点击配置班次方案,【1】班次方案中的数据来源于schedulingscheme关联下classesscheme模型的数据,【1】与预期一致,功能测试,P2,
班次方案-顺序验证,,班组排班方案 | 排班表配置 | 编辑 | 配置班次方案,已通过,陈树东,,【1】点击配置班次方案,【1】班次方案中的顺序与班次方案中的顺序一致,【1】与预期一致,功能测试,P2,
班次方案-默认值校验,,班组排班方案 | 排班表配置 | 编辑 | 配置班次方案,未通过,陈树东,,【1】点击配置班次方案,【1】班次方案默认选中第一个班次方案,【1】没有默认选中第一个班次方案,功能测试,P2,
班次方案-确认配置,,班组排班方案 | 排班表配置 | 编辑 | 配置班次方案,已通过,陈树东,,【1】点击空白处,【1】关闭配置班次方案弹窗,【1】直接选中班次方案极客确认配置,功能测试,P2,
清除班次-没有配置关联班组,,班组排班方案 | 排班表配置 | 编辑 | 配置班次方案,已通过,陈树东,,【1】选中已配置班次方案的日期点击上方的清除班次按钮,"【1】该日期的班次方案清除成功。
日历上不显示清除的班次方案信息",【1】与预期一致,功能测试,P0,
清除班次-已配置关联班组,,班组排班方案 | 排班表配置 | 编辑 | 配置班次方案,已通过,陈树东,,【1】点击清除班次,"【1】该日期的班次方案清除成功。
日历上不显示清除的班次方案信息",【1】与预期一致,功能测试,P0,
班次方案-时段信息存在重叠信息验证,,班组排班方案 | 排班表配置 | 编辑 | 配置班次方案,已通过,陈树东,"存在两个班次方案

方案1：早班6:00-18:00? ? ?夜班18:00-6:00

方案2:? 夜班0:00-8:00? ? ?早班：8:00-16:00? ?午班：16:00-24:00","【1】配置1号的班次方案为方案1
【2】配置方案2的班次方案为方案2","【1】支持
【2】支持","【1】与预期一致
【2】与预期一致",功能测试,P2,
班次方案-排班方案中没有班次方案,,班组排班方案 | 排班表配置 | 编辑 | 配置班次方案 | 场景,已通过,陈树东,"选中没有班次方案的排班方案

进入编辑状态",【1】点击配置班次方案,【1】配置界面中显示暂无数据,【1】与预期一致,功能测试,P2,
班次方案-配置班次有四个班次,,班组排班方案 | 排班表配置 | 编辑 | 配置班次方案 | 场景,已通过,陈树东,"排班方案有班次方案

进入编辑状态","【1】选中日期点击配置班次方案
【2】选中五班四倒班次方案
【3】点击空白处","【1】打开班次方案配置弹窗
【2】该班次方案为选中状态
【3】日历中所选日期显示所选班次方案的班次名称与时段信息
日历上方有清除班次按钮
","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P0,
班次方案-配置班次有跨天时段,,班组排班方案 | 排班表配置 | 编辑 | 配置班次方案 | 场景,已通过,陈树东,"排班方案有班次方案

进入编辑状态

班次方案中存在跨天时段","【1】选中日期点击配置班次方案
【2】选中存在跨天时段的班次
【3】点击空白处","【1】打开班次方案配置弹窗
【2】该班次方案为选中状态
【3】日历中所选日期显示所选班次方案的班次名称与时段信息
日历上方有清除班次按钮
","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P0,
配置关联班组-数据来源验证,,班组排班方案 | 排班表配置 | 编辑 | 配置关联班组,已通过,陈树东,,【1】选中班次点击配置关联班组,【1】关联班组中的数据来源于teamgroupinfo模型的数据,【1】与预期一致,功能测试,P2,
配置关联班组-顺序验证,,班组排班方案 | 排班表配置 | 编辑 | 配置关联班组,已通过,陈树东,,【1】选中班次点击配置关联班组,【1】班组选项中的顺序与班组列表中的顺序一致,【1】与预期一致,功能测试,P2,
配置关联班组-默认值校验,,班组排班方案 | 排班表配置 | 编辑 | 配置关联班组,已通过,陈树东,,【1】选中班次点击关联按钮,【1】关联班组默认选中暂不选择,【1】没有默认选中,功能测试,P2,
配置关联班组-确认配置,,班组排班方案 | 排班表配置 | 编辑 | 配置关联班组,阻塞,陈树东,,【1】点击空白处,【1】关闭配置关联班组弹窗,【1】直接选中配置即可直接配置,功能测试,P2,
配置关联班组-排班方案中没有班组,,班组排班方案 | 排班表配置 | 编辑 | 配置关联班组,已通过,陈树东,"选中没有班次方案的排班方案

进入编辑状态",【1】选中班次点击配置关联班组,【1】配置界面中显示暂不选择,【1】与预期一致,功能测试,P2,
班组关联唯一性校验,,班组排班方案 | 排班表配置 | 编辑 | 配置关联班组,已通过,陈树东,第一个班次已关联运维一班,【1】选中第二个班次点击关联,【1】已关联的班组可以继续配置,【1】与预期一致,功能测试,P2,
取消关联,,班组排班方案 | 排班表配置 | 编辑 | 配置关联班组,未执行,,,【1】点击关联班组后面的取消关联按钮,"【1】取消关联成功，班次的颜色恢复为默认颜色
取消关联班组可以重新选择",【1】,功能测试,P0,
配置关联班组,,班组排班方案 | 排班表配置 | 编辑 | 配置关联班组 | 场景,已通过,陈树东,,"【1】选中班次点击后方的关联按钮
【2】选中班组
【3】点击空白处","【1】打开关联班组选项
【2】班组为选中状态
【3】关闭关联选项
选中班次颜色与班组列表中的标签颜色一致","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P0,
批量设置班次按钮效果验证,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,已通过,陈树东,,【1】点击批量设置班次,【1】打开批量设置班次弹窗,【1】与预期一致,功能测试,P2,
批量设置班次-弹窗样式验证,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,已通过,陈树东,![Screenshot-0.png](https://tbfile.cet-electric.com:4753/thumbnail/013fea2aa97436093d652fd108807fb791ae/w/400/h/244 "400x244"),【1】进入批量设置班次弹窗,【1】弹窗样式与UI设计原型图一致,【1】与预期一致,功能测试,P2,
选择时段-选项范围验证,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,已通过,陈树东,"时间选择器选择为2025年1月

打开批量设置弹窗",【1】,【1】选择时段范围为2025年1月的范围内,【1】与预期一致,功能测试,P2,
选择时段-录入规则验证,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,已通过,陈树东,,"【1】开始时间<结束时间
【2】开始时间=结束时间
【3】开始时间>结束时间","【1】支持
【2】支持
【3】不支持","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
选择时段-录入方式验证,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,已通过,陈树东,,"【1】手动录入
【2】时间选择器","【1】不支持
【2】支持","【1】与预期一致
【2】与预期一致",功能测试,P2,
选择时段-必填项校验,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,已通过,陈树东,,"【1】开始结束时间都不设置
【2】只设置开始时间
【3】只设置结束时间","【1】不支持，保存时会有对应的提示信息
【2】不支持，保存时会有对应的提示信息
【3】不支持，保存时会有对应的提示信息","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
班次方案-数据来源验证,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,已通过,陈树东,,【1】点击配置班次方案,【1】班次方案中的数据来源于schedulingscheme关联下classesscheme模型的数据,【1】与预期一致,功能测试,P2,
班次方案-顺序验证,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,已通过,陈树东,,【1】点击配置班次方案,【1】班次方案中的顺序与班次方案中的顺序一致,【1】与预期一致,功能测试,P2,
班次方案-默认值校验,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,未执行,陈树东,,【1】点击配置班次方案,【1】班次方案默认选中第一个班次方案,【1】不会默认选中,功能测试,P2,
班次方案-确认配置,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,已通过,陈树东,,"【1】点击下拉框
【2】","【1】关闭配置班次方案弹窗
【2】",【1】与预期一致,功能测试,P2,
班次方案-排班方案中没有班次方案,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,已通过,陈树东,"选中没有班次方案的排班方案

进入编辑状态",【1】点击配置班次方案,【1】配置界面中显示暂无数据,【1】与预期一致,功能测试,P2,
取消按钮效果验证,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,已通过,陈树东,,"【1】点击取消按钮
【2】点击批量设置班次","【1】取消配置成功，编辑的内容不保存
【2】弹窗中的内容恢复为默认值，之前编辑的内容不保存","【1】与预期一致
【2】与预期一致",功能测试,P2,
关闭弹窗,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次,已通过,陈树东,,"【1】点击右上角的X图标
【2】点击批量设置班次","【1】关闭弹窗成功，编辑的内容不保存
【2】弹窗中的内容恢复为默认值，之前编辑的内容不保存","【1】与预期一致
【2】与预期一致",功能测试,P2,
批量设置班次,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次 | 场景,已通过,陈树东,,"【1】点击批量设置班次按钮
【2】选择时段为
2025-01-01~2025-01-10
【3】配置班次方案为四班三倒
【4】点击确定","【1】打开批量设置班次弹窗
【2】
【3】
【4】批量设置成功
日历中1~10号根据选中的班次方案显示。
显示班次名称与时段信息","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P0,
批量设置班次-配置已配置的时段,,班组排班方案 | 排班表配置 | 编辑 | 批量设置班次 | 场景,已通过,陈树东,日期2025年1月1日~2025年1月6日已配置班次,"【1】点击批量设置班次
【2】选择时段为2025年1月1日~2025年1月7日
【3】配置班次方案
【4】点击确定","【1】打开批量设置班次弹窗
【2】选择时段显示为2025年1月1日~2025年1月7日
【3】班次方案中显示配置的班次方案
【4】配置成功，日历中1~6日的班次信息被覆盖了
1~7日为显示为批量配置中选中班次的名称","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P2,
循环设置排班按钮效果验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,【1】点击循环设置排班按钮,【1】打开循环设置排班弹窗,【1】与预期一致,功能测试,P2,
循环设置排班-未配置班组,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,未通过,陈树东,月排班方案中未配置班组,【1】点击循环设置班组按钮,【1】提示请至少先配置一天的班组,【1】仍然可以进入批量设置班组页面,功能测试,P0,
循环设置排班-弹窗样式验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,![Screenshot-1.png](https://tbfile.cet-electric.com:4753/thumbnail/013f2e661616e3f714b188824b028c8b6699/w/200/h/203 "200x203"),【1】点击循环设置班组按钮,"【1】弹窗名称
说明
选择循环班组
循环班组预览框
循环时段选择框
开始班次
结束班次",【1】与预期一致,功能测试,P2,
循环班组范围验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,"【1】点击循环设置班组
【2】点击选择循环班组","【1】打开循环设置班组弹窗
【2】循环班组中只能选择已配置了班组的日期范围","【1】与预期一致
【2】支持所有日期，不做限制",功能测试,P1,
循环班组-录入规则验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,"【1】开始时间<结束时间
【2】开始时间=结束时间
【3】开始时间>结束时间","【1】支持
【2】支持
【3】不支持，保存时对应会有提示信息","【1】与预期一致
【2】与预期一致
【3】无法这样输入",功能测试,P2,
循环班组-录入方式验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,"【1】手动输入
【2】时间选择器选择","【1】不支持
【2】支持","【1】与预期一致
【2】与预期一致",功能测试,P2,
循环班组-必填项验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,"【1】只设置开始时间
【2】只设置结束时间
【3】开始时间与结束时间均不设置","【1】不支持，保存时会有提示信息
【2】不支持，保存时会有提示信息
【3】不支持，保存时会有提示信息","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
循环班组-时段配置校验,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,未执行,陈树东,"2025年1月1日~2025年1月5日为三班两倒

2025年1月6日为四班三倒","【1】1、打开循环设置班组
2、配置循环班组为2025年1月1日~2025年1月6日
【2】","【1】支持
【2】",,功能测试,P1,
循环班组预览-图例验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,已配置了循环班组,【1】,"【1】图例的颜色与名称与班组列表中的颜色与名称一致
且顺序与班组列表中的颜色一致",【1】与预期一致,功能测试,P2,
循环班组预览-格子数量过多时出现滚动条,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,已配置了循环班组,"【1】配置循环班组为
2025-01-01至2025-01-24
【2】配置循环班组为
2025-01-01至2025-01-25","【1】预览界面没有滚动条
【2】预览界面存在左右滚动条","【1】与预期一致
【2】与预期一致",功能测试,P2,
循环班组预览-班组颜色验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,"【1】点击循环设置班组
【2】配置循环班组时段为
2025-01-01至2025-01-06
【3】","【1】打开循环设置班组弹窗
【2】循环班组预览界面中显示1~6号的排班预览，其中格子的颜色与班组颜色一致
【3】","【1】与预期一致
【2】与预期一致",功能测试,P2,
循环班组预览-班组统计数目验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,"【1】点击循环设置班组
【2】配置循环班组为
2025-01-01至2025-01-06
【3】","【1】打开循环设置班组弹窗
【2】循环班组预览下方的统计数量与选择的循环班组一致
【3】","【1】与预期一致
【2】与预期一致",功能测试,P2,
循环班组预览-班次不平均时提示信息验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,【1】设置班次分配不均的循环班组,【1】在预览界面下方中会存在提示信息【目前各班组值班数量不平均】,【1】与预期一致,功能测试,P2,
循环时段-范围校验,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,循环班组配置了2025-01-01至2025-01-06,【1】,"【1】循环时段的范围为
2025.01.01~2025.01.31",【1】与预期一致,功能测试,P2,
循环时段-录入规则验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,"【1】开始时间<结束时间
【2】开始时间=结束时间
【3】开始时间>结束时间","【1】支持
【2】支持
【3】不支持，确定保存时会有提示信息","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
循环时段-录入方式验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,"【1】手动输入
【2】时间选择器设置","【1】不支持，确定保存时会有提示信息
【2】支持","【1】与预期一致
【2】与预期一致",功能测试,P2,
循环时段-必填项验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,"【1】开始时间与结束时间都不设置
【2】只设置开始时间
【3】只设置结束时间","【1】不支持，确定保存时会有提示信息
【2】不支持，确定保存时会有提示信息
【3】不支持，确定保存时会有提示信息","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
开始班次-选项方式确认,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,【1】,【1】开始班次配置项为下拉选项,【1】与预期一致,功能测试,P2,
开始班次-数据来源验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,【1】,【1】开始班次中的数据来源于循环班组中的配置的班次方案班次,【1】与预期一致,功能测试,P2,
开始班次-顺序验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,【1】点击开始班次配置下拉项,【1】下拉选项中的顺序与班次方案中的班次顺序一致,【1】与预期一致,功能测试,P2,
开始班次-默认值校验,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,【1】,【1】开始默认选中第一个班次,【1】与预期一致,功能测试,P2,
开始班次-确认配置,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,【1】点击下拉配置项,【1】关闭开始班次下拉配置项,【1】与预期一致,功能测试,P2,
结束班次-数据来源验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,【1】,【1】结束班次中的数据来源于循环班组中的配置的班次方案班次,【1】与预期一致,功能测试,P2,
结束班次-顺序验证,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,【1】点击结束班次配置下拉项,【1】下拉选项中的顺序与班次方案中的班次顺序一致,【1】与预期一致,功能测试,P2,
结束班次-默认值校验,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,【1】,【1】结束班次默认选中最后一个班次,【1】与预期一致,功能测试,P2,
结束班次-确认配置,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,【1】点击下拉配置项,【1】关闭结束班次下拉配置项,【1】与预期一致,功能测试,P2,
结束班次-选项方式确认,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,【1】,【1】结束班次配置项为下拉选项,【1】与预期一致,功能测试,P2,
取消按钮,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,"【1】点击取消按钮
【2】点击循环设置班组按钮","【1】取消配置成功，编辑的内容不保存
【2】打开循环设置班组按钮，之前配置的内容不保存，弹窗恢复为默认内容","【1】与预期一致
【2】与预期一致",功能测试,P2,
关闭弹窗,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班,已通过,陈树东,,"【1】点击右上角的关闭图表
【2】点击循环设置班组按钮","【1】关闭弹窗成功，编辑的内容不保存
【2】打开循环设置班组按钮，之前配置的内容不保存，弹窗恢复为默认内容","【1】与预期一致
【2】与预期一致",功能测试,P2,
循环设置排班,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班 | 场景,已通过,陈树东,,"【1】点击循环设置排班按钮
【2】设置选择循环班组
为2025-01-01至2025-01-10
【3】设置循环时段
2025-01-11至2025-01-30
【4】配置开始班次
【5】配置结束班次
【6】点击确定","【1】打开循环设置排班弹窗
【2】下方循环班组预览界面中展示预览效果
【3】
【4】
【5】
【6】循环设置排班成功，日历上根据循环设置排班配置显示","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致",功能测试,P0,
循环设置排班-循环时段已配置了班次,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班 | 场景,已通过,陈树东,时间2025年2月10日设置了班次,"【1】点击循环设置排班按钮
【2】选择循环班组为
2025.02.01-2025.01.07
【3】设置循环时段为
2025.02.10-2025.02.17
【4】设置开始班次为
早班
【5】设置结束班次为
夜班
【6】点击确定","【1】打开循环设置排班弹窗
【2】
【3】
【4】
【5】
【6】设置成功，日历上显示循环配置排班配置。
2025.02.10之前的配置被覆盖了","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致",功能测试,P2,
循环设置排班-修改默认班次顺序,,班组排班方案 | 排班表配置 | 编辑 | 循环设置排班 | 场景,未通过,,,"【1】点击循环设置排班按钮
【2】选择循环班组为
2025.02.01-2025.01.07
【3】设置循环时段为
2025.02.10-2025.02.17
【4】设置开始班次为
中班
【5】设置结束班次为
早班
【6】点击确定","【1】打开循环设置排班弹窗
【2】
【3】
【4】
【5】
【6】设置成功，日历上显示循环配置排班配置。
",,功能测试,P2,
未配置排班表效果展示,,班组排班方案 | 排班表配置 | 展示,已通过,陈树东,,【1】访问排班表页面,【1】页面与原型设计图一致,【1】与预期一致,功能测试,P2,
排班表-日历信息验证,,班组排班方案 | 排班表配置 | 展示,已通过,陈树东,,"【1】
【2】切换月份","【1】日历的星期信息显示正确
【2】日历的星期信息显示正确","【1】与预期一致
【2】与预期一致",功能测试,P2,
排班表日期标识验证,,班组排班方案 | 排班表配置 | 展示,已通过,陈树东,,【1】进入排班表页面,【1】节假日日期有特殊标识、当天也有特定标识,【1】当天会高亮展示,功能测试,P0,
运维人员查看排班表效果验证,,班组排班方案 | 排班表配置 | 展示,未通过,陈树东,,"【1】运维人员查看排班表
【2】","【1】只能查看自己排班的时段信息，其余排班信息都显示为休班
【2】",【1】运维人员可以查看不属于自己的排班信息,功能测试,P0,
生产人员查看排班表效果验证,,班组排班方案 | 排班表配置 | 展示,未通过,陈树东,,【1】使用生产组人员查看排班表,【1】可以查看所有的排班信息,【1】生产人员没有编辑权限时无法查看排班表信息,功能测试,P0,
取消编辑,,班组排班方案 | 排班表配置 | 取消编辑,已通过,陈树东,,"【1】点击取消编辑按钮
【2】点击确定","【1】弹出取消编辑确认弹窗
【2】取消编辑成功
编辑的班次方案与班组关联不保存。
日历上不更新编辑信息
数据表schedulingclasses不更新数据
数据表eemoperationlog 中不存在编辑操作记录","【1】与预期一致
【2】与预期一致",功能测试,P0,
返回编辑,,班组排班方案 | 排班表配置 | 取消编辑,已通过,陈树东,,"【1】点击取消编辑按钮
【2】点击返回按钮","【1】弹出编辑确认弹窗
【2】返回排班表编辑界面。之前编辑的内容仍然存在","【1】与预期一致
【2】与预期一致",功能测试,P2,
没有编辑内容点击取消编辑,,班组排班方案 | 排班表配置 | 取消编辑,已通过,陈树东,排班表没有配置班次与班组,【1】点击取消编辑,【1】直接退出编辑状态，返回预览界面,【1】与预期一致,功能测试,P2,
保存,,班组排班方案 | 排班表配置 | 保存,已通过,陈树东,,"【1】点击保存按钮
【2】","【1】保存成功，返回到预览状态
编辑状态中编辑的内容保存到数据库中
数据表schedulingclasses中存在编辑的数据
操作日志表eemoperationlog 中存在操作刚刚的编辑记录

【2】",【1】与预期一致,功能测试,P0,
排班表没有编辑内容，点击保存-保存逻辑验证,,班组排班方案 | 排班表配置 | 保存,已通过,陈树东,第一次编辑该月份与排班方案的排班表,【1】点击保存按钮,"【1】不会调用接口
数据表中schedulingclasses不会新增数据",【1】会调用接口，但是不会插入数据,功能测试,P0,
样式验证,,班组排班方案 | 月班组能耗 | 排班方案筛选框,已通过,陈树东,,【1】,【1】与UI原型图一致，选项为下拉筛选,【1】与预期一致,功能测试,P2,
数据来源验证,,班组排班方案 | 月班组能耗 | 排班方案筛选框,已通过,陈树东,,【1】,【1】数据只显示班组类型为生产班组的排班方案,【1】与预期一致,功能测试,P2,
数据顺序验证,,班组排班方案 | 月班组能耗 | 排班方案筛选框,已通过,陈树东,,【1】,【1】选项数据与排班方案的创建顺序一致,【1】与预期一致,功能测试,P2,
默认值验证,,班组排班方案 | 月班组能耗 | 排班方案筛选框,已通过,陈树东,,【1】,【1】默认选中第一个排班方案,【1】与预期一致,功能测试,P2,
排班方案名称最长-展示效果验证,,班组排班方案 | 月班组能耗 | 排班方案筛选框,已通过,陈树东,排班方案名称为20个字符,"【1】点击下拉选项
【2】选中该排班方案","【1】下拉选项中超出部分会省略显示
【2】在筛选框中超出部分会省略号显示","【1】与预期一致
【2】与预期一致",功能测试,P2,
排班方案是否支持切换验证,,班组排班方案 | 月班组能耗 | 排班方案筛选框,已通过,陈树东,,【1】选中其他排班方案,【1】支持，筛选框显示为选中排班方案,【1】与预期一致,功能测试,P1,
样式验证,,班组排班方案 | 月班组能耗 | 能源类型筛选框,已通过,陈树东,,【1】,【1】与UI原型图一致，选项为下拉筛选,【1】与预期一致,功能测试,P2,
数据来源验证,,班组排班方案 | 月班组能耗 | 能源类型筛选框,已通过,陈树东,,【1】,【1】数据来源于项目设置中能源类型数据,【1】与预期一致,功能测试,P2,
数据顺序验证,,班组排班方案 | 月班组能耗 | 能源类型筛选框,已通过,陈树东,,【1】,【1】选项数据中与项目设置中能源类型的顺序一致,【1】与预期一致,功能测试,P2,
默认值验证,,班组排班方案 | 月班组能耗 | 能源类型筛选框,已通过,陈树东,,【1】,【1】默认选中第一个能源类型,【1】与预期一致,功能测试,P2,
能源类型是否支持切换验证,,班组排班方案 | 月班组能耗 | 能源类型筛选框,已通过,陈树东,,【1】选中其他能源类型,【1】支持，筛选框显示为选中能源类型,【1】与预期一致,功能测试,P1,
节点树数据确认,,班组排班方案 | 月班组能耗 | 关联节点,未通过,陈树东,,【1】来源,"【1】关联节点数据来源于
schedulingschemetonode",【1】目前展示所有的节点,功能测试,P2,
节点树默认样式校验,,班组排班方案 | 月班组能耗 | 关联节点,已通过,陈树东,,【1】进入关联节点弹窗,【1】节点树默认为展开状态,【1】节点树默认为未展开状态,功能测试,P2,
节点树完整性验证,,班组排班方案 | 月班组能耗 | 关联节点,已通过,陈树东,,【1】完整性,【1】存在滚动条，完整展示,【1】与预期一致,功能测试,P2,
权限验证,,班组排班方案 | 月班组能耗 | 关联节点,已通过,陈树东,,"【1】登录用户拥有根节点权限
【2】登录用户仅拥有部分节点权限
【3】登录用户无节点权限","【1】全部节点可查看
【2】可查看部分节点
【3】不可看","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P0,
节点树-容错性测试,,班组排班方案 | 月班组能耗 | 关联节点,已通过,陈树东,,"【1】节点过多超出弹窗大小时
【2】节点名称过长时","【1】左右滚动条
【2】超出部分省略号显示","【1】出现滚动条
【2】出现滚动条",功能测试,P2,
节点树是否支持选中,,班组排班方案 | 月班组能耗 | 关联节点,已通过,陈树东,,【1】选中节点树节点,【1】选中节点为选中状态,【1】与预期一致,功能测试,P2,
节点树-默认选中节点,,班组排班方案 | 月班组能耗 | 关联节点,已通过,陈树东,,【1】,【1】默认选中根节点,【1】与预期一致,功能测试,P2,
能源类型过滤验证,,班组排班方案 | 月班组能耗 | 关联节点,未通过,陈树东,,"【1】管理节点关联了配电房设备
【2】该管理节点关联了管道压空","【1】能源类型选择电/折标/二氧化碳时，展示该节点及其父节点
【2】能源类型选择压空/折标/二氧化碳时，展示该节点及其父节点","【1】无法正确展示出节点树
【2】无法正确展示出节点树",功能测试,P2,
搜索框-搜索范围,,班组排班方案 | 月班组能耗 | 关联节点,已通过,陈树东,,【1】,【1】针对节点名称进行检索,【1】与预期一致,功能测试,P2,
搜索框-支持输入字符验证,,班组排班方案 | 月班组能耗 | 关联节点,已通过,陈树东,,"【1】输入中文字符
【2】输入英文字符
【3】输入数字字符
【4】输入符号字符","【1】支持
【2】支持
【3】支持
【4】支持","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P2,
搜索框-搜索效果验证,,班组排班方案 | 月班组能耗 | 关联节点,已通过,陈树东,,"【1】输入为空
【2】输入包含字符
【3】输入不存在的字符
【4】删除部分字符
【5】删除全部字符","【1】显示所有节点
【2】显示名称含有包含字符的节点与其父节点
【3】节点树显示为空
【4】根据删除后的字符进行搜索
【5】显示所有节点","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致",功能测试,P1,
时间选择器-样式确认,,班组排班方案 | 月班组能耗 | 时间选择器,已通过,陈树东,,【1】,【1】与UI原型图一致,【1】更新为以下样式,功能测试,P2,
切换分析周期,,班组排班方案 | 月班组能耗 | 时间选择器,已通过,陈树东,,"【1】切换分析周期为年
【2】切换分析周期为月","【1】分析周期选中为年
时间选框中默认选中为当前年份
【2】分析周期选中为月
时间选框中默认选中为当前月份","【1】与预期一致
【2】与预期一致",功能测试,P2,
默认值校验,,班组排班方案 | 月班组能耗 | 时间选择器,已通过,陈树东,,【1】进入排班表页面,"【1】分析周期默认为选择为月
时间选择器默认选择为当前月份",【1】与预期一致,功能测试,P0,
月-筛选范围校验,,班组排班方案 | 月班组能耗 | 时间选择器,已通过,陈树东,,"【1】选择为过去月份
【2】选择为现在月份
【3】选择为未来月份","【1】支持
【2】支持
【3】不支持","【1】与预期一致
【2】与预期一致
【3】支持筛选未来月份",功能测试,P2,
月-录入方式验证,,班组排班方案 | 月班组能耗 | 时间选择器,已通过,陈树东,,"【1】手动录入
【2】时间选择器","【1】不支持
【2】支持","【1】与预期一致
【2】与预期一致",功能测试,P2,
年-筛选范围校验,,班组排班方案 | 月班组能耗 | 时间选择器,已通过,陈树东,,"【1】选择为过去年份
【2】选择为现在年份
【3】选择为未来年份","【1】支持
【2】支持
【3】不支持","【1】与预期一致
【2】与预期一致
【3】支持筛选未来年份",功能测试,P2,
年-录入方式验证,,班组排班方案 | 月班组能耗 | 时间选择器,已通过,陈树东,,"【1】手动录入
【2】时间选择器","【1】不支持
【2】支持","【1】与预期一致
【2】与预期一致",功能测试,P2,
总能耗统计样式确认,,班组排班方案 | 月班组能耗 | 总能耗统计,已通过,陈树东,,【1】,【1】与UI原型图一致,【1】与预期一致,功能测试,P2,
总班组能耗数据准确性验证,,班组排班方案 | 月班组能耗 | 总能耗统计,已通过,陈树东,"select value

from teamgroupenergy

where logtime=“时间”

and **aggregationCycle=“聚合周期”**

**and&nbsp;**energytype=“能源类型”

**and&nbsp;**objectlabel=“节电类型”

and objectid=“节点id”

and classid is null

and teamgroupid is null","【1】
【2】","【1】总班组能耗数据准确性验证
【2】",【1】与预期一致,功能测试,P0,
总班次数据准确性验证,,班组排班方案 | 月班组能耗 | 总能耗统计,未通过,陈树东,,【1】,"【1】总班次数据=
select count(*) from schedulingclasses",【1】,功能测试,P0,
平均班次能耗数据准确性验证,,班组排班方案 | 月班组能耗 | 总能耗统计,已通过,陈树东,,【1】,【1】平均班次能耗=总班组能耗/总班次,【1】与预期一致,功能测试,P0,
数据精度验证,,班组排班方案 | 月班组能耗 | 总能耗统计,已通过,陈树东,,【1】,【1】能耗数据保留两位小数位，四舍五入保留,【1】与预期一致,功能测试,P2,
班组用能卡片样式确认,,班组排班方案 | 月班组能耗 | 班组用能分析卡片,已通过,陈树东,,【1】,【1】样式与UI设计原型图一致,【1】与预期一致,功能测试,P2,
卡片数量确认,,班组排班方案 | 月班组能耗 | 班组用能分析卡片,已通过,陈树东,,【1】,【1】卡片数量与排班表中关联的班组数量一致,【1】与预期一致,功能测试,P2,
班组数量少于三个效果验证,,班组排班方案 | 月班组能耗 | 班组用能分析卡片,已通过,陈树东,班组数量少于三个,【1】,【1】班组用能分析卡片展示所有的班组数据，剩余部分为空白填充，页面没有上一个下一个按钮,【1】与预期一致,功能测试,P2,
班组数量为0时，展示效果,,班组排班方案 | 月班组能耗 | 班组用能分析卡片,已通过,陈树东,,【1】排班表中不配置关联班组,【1】班组用能分析卡片为空,【1】与预期一致,功能测试,P2,
上一个按钮功能验证,,班组排班方案 | 月班组能耗 | 班组用能分析卡片,已通过,陈树东,,"【1】卡片已经为第一个卡片
【2】卡片数量少于3个
【3】卡片不是第一个卡片","【1】按钮可以点击，点击之后查看最后一张卡片信息
【2】上一个按钮无法点击
【3】点击上一个按钮之后查看上一个卡片","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
下一个按钮功能验证,,班组排班方案 | 月班组能耗 | 班组用能分析卡片,已通过,陈树东,,"【1】卡片已经为最后一个卡片
【2】卡片数量少于3个
【3】卡片不是最后一个卡片","【1】下一个按钮可以点击，点击之后查看第一个卡片
【2】下一个按钮无法点击
【3】点击下一个按钮之后查看下一个卡片","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
总能耗数据验证,,班组排班方案 | 月班组能耗 | 班组用能分析卡片,已通过,陈树东,"select value

from teamgroupenergy

where logtime=“时间”

and?aggregationCycle=“聚合周期”

and?energytype=“能源类型”

and?objectlabel=“节电类型”

and objectid=“节点id”

and?**teamgroupid=""班组id""**",【1】,【1】总能耗数据与数据库数据一致,【1】与预期一致,功能测试,P0,
用能占比数据验证,,班组排班方案 | 月班组能耗 | 班组用能分析卡片,已通过,陈树东,,【1】,【1】用能占比数据为班组总能耗/总班组能耗,【1】与预期一致,功能测试,P0,
总班次数据验证,,班组排班方案 | 月班组能耗 | 班组用能分析卡片,已通过,陈树东,"select count（\*）

from schedulingclasses

where **teamgroupid=""班组id\*\***""\*\*

and **schedulingschemeid=""排班方案id""**

**and&nbsp;&nbsp;logtime=“节假日日期”**",【1】,【1】班组总班次与数据库查询的数据一致,【1】与预期一致,功能测试,P0,
平均班次能耗数据验证,,班组排班方案 | 月班组能耗 | 班组用能分析卡片,已通过,陈树东,,【1】,【1】平均班组能耗数据为总能耗/总班次,【1】与预期一致,功能测试,P0,
卡片顺序验证,,班组排班方案 | 月班组能耗 | 班组用能分析卡片,未通过,陈树东,,"【1】
【2】","【1】平均班次能耗越低的卡片排在前面
【2】平均班次能耗相等时，根据班次id升序排序",【1】,功能测试,P0,
能耗精度验证,,班组排班方案 | 月班组能耗 | 班组用能分析卡片,已通过,陈树东,,"【1】用能占比
【2】总能耗
【3】平均班次能耗","【1】保留一位小数位
【2】保留两位小数位
【3】保留两位小数位","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
排名图样式确认,,班组排班方案 | 月班组能耗 | 平均班次能耗排名图,已通过,陈树东,,【1】,【1】排名图与UI原型设计图一致,【1】与预期一致,功能测试,P2,
班组过多时出现滚动条,,班组排班方案 | 月班组能耗 | 平均班次能耗排名图,未通过,陈树东,,【1】分析排班方案存在10个班组,【1】在排名图中出现竖状滚动条,【1】不会出现滚动条,功能测试,P2,
排名图平均班次能耗数据准确性验证,,班组排班方案 | 月班组能耗 | 平均班次能耗排名图,已通过,陈树东,,【1】,【1】验证各班组平均班次能耗数据准确性,【1】与预期一致,功能测试,P0,
排名图元素确认,,班组排班方案 | 月班组能耗 | 平均班次能耗排名图,已通过,陈树东,,【1】,【1】坐标轴名称、刻度、单位与预期一致,【1】与预期一致,功能测试,P2,
浮动窗口数据验证,,班组排班方案 | 月班组能耗 | 平均班次能耗排名图,未通过,陈树东,,【1】移动到排名图条状,【1】浮动窗口中显示维度名称、指标名称、指标值与单位,【1】没有单位,功能测试,P2,
班次对比样式确认,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,![Screenshot-2.png](https://tbfile.cet-electric.com:4753/thumbnail/013f3c431390eb71eac3bc6a371bb6d8a5b5/w/800/h/136 "800x136"),"【1】班次对比名称
【2】班次对比说明
【3】班次对比卡片样式","【1】与原型一致
【2】与原型一致
【3】与原型一致","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
班次卡片-数量效果验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,"【1】分析时段的班次多于四个
【2】分析时段的班次为3个","【1】班次对比卡片只显示前四个班次对比卡片
【2】只显示3个班次对比卡片，第四个卡片位置为空白","【1】
【2】与预期一致",功能测试,P2,
班次卡片顺序验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,【1】,【1】根据排班表中配置时间以及班次id进行展示,【1】与预期一致,功能测试,P1,
配置界面样式确认,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,"【1】点击班次对比卡片配置按钮
【2】","【1】弹窗左侧显示班组列表，弹窗右侧显示配置的所有班次方案
【2】",【1】与预期一致,功能测试,P2,
配置界面-班组列表数据来源,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,"select?**teamgroupid**

**from&nbsp;schedulingclasses**

**where&nbsp;**classconfigid=""班次id""****

****and&nbsp;&nbsp;**schedulingschemeid=“排班方案id”******

******and&nbsp;**logtime=“日期时间”********",【1】,"【1】生产班组列表中的数据与
schedulingclasses表中的数据一致",【1】与预期一致,功能测试,P2,
配置界面-班组列表顺序验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,【1】,【1】班组列表中的顺序根据班组id进行排序,【1】与预期一致,功能测试,P2,
配置界面-班组列表默认值验证,,班组排班方案 | 月班组能耗 | 班次对比,待评审,,,【1】,【1】默认勾选所有班组,【1】,功能测试,P2,
配置界面-班组列表是否可以修改,,班组排班方案 | 月班组能耗 | 班次对比,待评审,,,"【1】取消选中班组
【2】重新选中班组","【1】支持
【2】支持",【1】,功能测试,P2,
配置界面-班组列表选项数量验证,,班组排班方案 | 月班组能耗 | 班次对比,待评审,,,"【1】全部取消
【2】多选
【3】单选
【4】全选","【1】不支持，最后一个班组无法取消
【2】支持
【3】支持
【4】支持",,功能测试,P2,
配置界面-班次列表数据来源,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,"select ****classconfigid****

**from&nbsp;schedulingclasses**

**\*\*and&nbsp;&nbsp;**schedulingschemeid=“排班方案id”**\*\***

**\*\***and?**logtime=“日期时间”**\*\*****",【1】,"【1】生产班组列表中的数据与
schedulingclasses表中的数据一致",【1】与预期一致,功能测试,P2,
配置界面-班次列表顺序验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,【1】,【1】班次列表中的顺序根据班次方案id与班次id进行排序,【1】与预期一致,功能测试,P2,
配置界面-班次列表默认值验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,【1】,【1】配置默认根据卡片位置勾选第几个班次,【1】与预期一致,功能测试,P2,
配置界面-班次列表是否可以修改,,班组排班方案 | 月班组能耗 | 班次对比,未通过,陈树东,,"【1】取消选中班次
【2】重新选中班次","【1】不支持
【2】支持","【1】与预期一致
【2】与预期一致",功能测试,P2,
配置界面-班次列表选项数量验证,,班组排班方案 | 月班组能耗 | 班次对比,未通过,,,"【1】全部取消
【2】多选
【3】单选
【4】全选","【1】不支持，最后一个班组无法取消
【2】不支持
【3】支持
【4】不支持",【1】,功能测试,P2,
班次名称-准确性验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,【1】,【1】班次对比卡片中班次名称与选中班次一致,【1】与预期一致,功能测试,P2,
班次名称-长度过长时显示验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,【1】班次名称为20字符,【1】超出部分省略号显示,【1】超出时会换行显示,功能测试,P2,
班次方案名称-准确性验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,【1】,【1】班次对比卡片中班次方案名称与选中班次方案一致,【1】与预期一致,功能测试,P2,
班次方案名称-长度过长时显示验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,【1】班次方案名称为20字符,【1】超出部分省略号显示,【1】过长时会换行显示,功能测试,P2,
班组列表-数据准确性验证,,班组排班方案 | 月班组能耗 | 班次对比,未通过,陈树东,,【1】,【1】班组列表与配置中勾选的班组名称一致,【1】不会默认勾选班组,功能测试,P2,
班组名称-长度过长时显示验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,【1】班组名称长度过长时,【1】超出部分省略号显示,【1】超出部分换行显示,功能测试,P2,
切换能源类型-用电量名称更新,,班组排班方案 | 月班组能耗 | 班次对比,未通过,陈树东,,【1】切换能源类型为折标煤,【1】班次对比卡片中的用能量更新为用折标煤量,【1】,功能测试,P2,
用电量数据准确性验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,"select value

from teamgroupenergy

where logtime=“时间”

and aggregationCycle=“聚合周期”

and energytype=“能源类型”

and objectlabel=“节电类型”

and objectid=“节点id”

and?**classid**\=""班次id""",【1】,【1】用电量数据与数据库查询数据一致,【1】与预期一致,功能测试,P0,
用能占比数据准确性校验,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,【1】,【1】用能占比=用电量/总班组能耗*100%,【1】与预期一致,功能测试,P0,
平均班次能耗数据准确性验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,【1】,【1】平均班次能耗=用电量/所选生产班组的班次总数,【1】与预期一致,功能测试,P0,
班次对比卡片-数据精度验证,,班组排班方案 | 月班组能耗 | 班次对比,已通过,陈树东,,"【1】用能占比
【2】用电量
【3】平均班次能耗","【1】保留一位小数位
【2】保留两位小数位
【3】保留两位小数位","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P2,
对比相同班组不同班次的数据,,班组排班方案 | 月班组能耗 | 班次对比 | 场景,待评审,,,"【1】配置第一个卡片为生产1班-早班
【2】配置第二个卡片为
生产1班-中班
【3】配置第三个卡片为
生产1班-午班
【4】配置第四个卡片为
生产1班-夜班","【1】第一个卡片展示生产1班的早班用能数据
【2】第二个卡片展示生产1班的中班用能数据
【3】第三个卡片展示生产1班的午班用能数据
【4】第四个卡片展示生产1班的夜班用能数据",,功能测试,P0,
对比不同班组相同班次的数据,,班组排班方案 | 月班组能耗 | 班次对比 | 场景,待评审,,,"【1】配置第一个卡片为生产1班-早班
【2】配置第二个卡片为
生产2班-早班
【3】配置第二个卡片为
生产3班-早班
【4】配置第二个卡片为
生产4班-早班","【1】第一个卡片展示生产1班的早班用能数据
【2】第二个卡片展示生产2班的早班用能数据
【3】第三个卡片展示生产3班的早班用能数据
【4】第四个卡片展示生产4班的早班用能数据",,功能测试,P0,
班组能耗柱状图-样式确认,,班组排班方案 | 月班组能耗 | 班组能耗柱状图,已通过,陈树东,![Screenshot-3.png](https://tbfile.cet-electric.com:4753/thumbnail/013f43f5c827fecb09f9e6e8163d077e2cbd/w/800/h/178 "800x178"),【1】,【1】与UI原型图一致,【1】与预期一致,功能测试,P2,
纵坐标-单位自动转换验证,,班组排班方案 | 月班组能耗 | 班组能耗柱状图,已通过,陈树东,"1000kwh=1mwh

1000000kwh=1Gwh","【1】当能耗超过1000kwh时
【2】当能耗超过1000000kwh时","【1】自动转换为mwh
【2】自动转换为1gwh","【1】与预期一致
【2】与预期一致",功能测试,P2,
图例验证,,班组排班方案 | 月班组能耗 | 班组能耗柱状图,已通过,陈树东,,【1】图例名称与颜色,【1】与排班表中关联的班组名称与颜色一致,【1】与预期一致,功能测试,P2,
图例点击,,班组排班方案 | 月班组能耗 | 班组能耗柱状图,已通过,陈树东,,【1】点击班组能耗详情图的班组图例,【1】所选图例在图表中不显示,【1】与预期一致,功能测试,P1,
班组能耗详情图-数据准确性验证,,班组排班方案 | 月班组能耗 | 班组能耗柱状图,已通过,陈树东,,【1】,【1】班组能耗详情图与teamgroupenergy查询出来的数据一致,【1】与预期一致,功能测试,P0,
能耗柱形图-分析周期验证,,班组排班方案 | 月班组能耗 | 班组能耗柱状图,已通过,陈树东,,"【1】分析周期为月
【2】分析周期为年","【1】横坐标维度为日
【2】横坐标维度为月","【1】与预期一致
【2】与预期一致",功能测试,P2,
滚动条条件验证,,班组排班方案 | 月班组能耗 | 班组能耗柱状图,已通过,陈树东,,"【1】分析维度超过20个时
【2】分析维度少于等于20个时","【1】图表下方会有左右滚动条
【2】没有滚动条","【1】与预期一致
【2】与预期一致",功能测试,P2,
浮动窗口数据验证,,班组排班方案 | 月班组能耗 | 班组能耗柱状图,待评审,,,【1】移动到班组能耗柱状图,【1】浮动窗口中显示维度名称、指标名称、指标值与单位,【1】不会显示单位,功能测试,P2,
参与对象,,班组排班方案 | 统计逻辑验证,已通过,陈树东,,【1】,【1】关联了排班方案的所有管理层级节点，到用能设备,【1】与预期一致,功能测试,P1,
参与数据-电,,班组排班方案 | 统计逻辑验证,已通过,陈树东,关联配电房下官网设备,"【1】末端节点
【2】非末端节点（自身未关联表计）
【3】非末端节点（自身关联表计，且子级全部关联排班方案）
【4】非末端节点（自身关联表计，子级部分关联排班方案）","【1】关联表计中dataid=4000004的定时记录
【2】子级关联表计中dataid=4000004的定时记录
【3】本身关联表计中dataid=4000004的定时记录
【4】关联排班方案中子级关联表计中dataid=4000004的定时记录","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P0,
参与数据-能源,,班组排班方案 | 统计逻辑验证,已通过,陈树东,关联管道房下官网设备,"【1】末端节点
【2】非末端节点（自身未关联表计）
【3】非末端节点（自身关联表计，且子级全部关联排班方案）
【4】非末端节点（自身关联表计，子级部分关联排班方案）","【1】关联表计中dataid=10000001的定时记录
【2】子级关联表计中dataid=10000001的定时记录
【3】本身关联表计中dataid=10000001的定时记录
【4】关联排班方案中子级关联表计中dataid=10000001的定时记录","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P0,
子节点数据获取是否正确-电,,班组排班方案 | 统计逻辑验证,已通过,陈树东,管网设备为配电房下方官网设备,"【1】""末端节点：
1.管理层级节点关联排班方案；√
2.管理层级节点关联管网；√
3.管网设备关联采集设备；√
4.采集设备关联测点 4000004；√""

【2】""末端节点：
1.管理层级节点关联排班方案；x
2.管理层级节点关联管网；√
3.管网设备关联采集设备；√
4.采集设备关联测点 4000004；√""

【3】""末端节点：
1.管理层级节点关联排班方案；√
2.管理层级节点关联管网；x
3.管网设备关联采集设备；√
4.采集设备关联测点 4000004；√""

【4】""末端节点：
1.管理层级节点关联排班方案；√
2.管理层级节点关联管网；√
3.管网设备关联采集设备；x
4.采集设备关联测点 4000004；√""

【5】""末端节点：
1.管理层级节点关联排班方案；√
2.管理层级节点关联管网；√
3.管网设备关联采集设备；√
4.采集设备关联测点 4000004；x""
","【1】正确获取  dataid=4000004的能耗值
【2】不计算该节点的班次班组能耗
【3】不计算该节点的班次班组能耗
【4】不计算该节点的班次班组能耗
【5】不计算该节点的班次班组能耗","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致",功能测试,P1,
子节点数据获取是否正确-其他,,班组排班方案 | 统计逻辑验证,已通过,陈树东,管网设备为管道房下方官网设备,"【1】""末端节点：
1.管理层级节点关联排班方案；√
2.管理层级节点关联管网；√
3.管网设备关联采集设备；√
4.采集设备关联测点 10000001；√""

【2】""末端节点：
1.管理层级节点关联排班方案；x
2.管理层级节点关联管网；√
3.管网设备关联采集设备；√
4.采集设备关联测点 10000001；√""

【3】""末端节点：
1.管理层级节点关联排班方案；√
2.管理层级节点关联管网；x
3.管网设备关联采集设备；√
4.采集设备关联测点 10000001；√""

【4】""末端节点：
1.管理层级节点关联排班方案；√
2.管理层级节点关联管网；√
3.管网设备关联采集设备；x
4.采集设备关联测点 4000004；√""

【5】""末端节点：
1.管理层级节点关联排班方案；√
2.管理层级节点关联管网；√
3.管网设备关联采集设备；√
4.采集设备关联测点 4000004；x""
","【1】正确获取  dataid=10000001的能耗值
【2】不计算该节点的班次班组能耗
【3】不计算该节点的班次班组能耗
【4】不计算该节点的班次班组能耗
【5】不计算该节点的班次班组能耗","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致",功能测试,P1,
父节点数据获取是否正确,,班组排班方案 | 统计逻辑验证,已通过,陈树东,,"【1】""非末端节点：
1.子节点全部关联排班方案；√
2.本身管理层级关联管网；√

【2】""非末端节点：
1.子节点全部关联排班方案；x
2.本身管理层级关联管网；√
","【1】数据为本身关联表计的定时记录
【2】数据为关联排班方案子节点关联表计的定时记录","【1】与预期一致
【2】与预期一致",功能测试,P1,
班次班组能耗班次时间与班次信息获取是否正确,,班组排班方案 | 统计逻辑验证,已通过,陈树东,,"【1】班次信息：班次开始时间、班次结束时间、班次id
【2】班组信息：班组id","【1】开始时间=排班表中时间-班次方案中时间的配置
结束时间=排班表中时间-班次方案中时间的配置
班次id=classesconfig中关联班次的班次id
【2】班组id=teamgroupinfo中配置关联班组的班组id","【1】与预期一致
【2】与预期一致",功能测试,P1,
班次班组能耗值计算是否正确,,班组排班方案 | 统计逻辑验证,已通过,陈树东,,"【1】1.节点为末端节点
2.关联官网设备且有dataid=4000004测点
3.排班表中配置了关联班次与关联班组
【2】1.节点为非末端节点
2.子节点全部关联排班方案
3.节点本身关联官网设备且有dataid=4000004测点
4.排班表中配置了关联班次与关联班组
【3】1.节点为非末端节点
2.子节点全部关联排班方案
3.节点本身不关联官网设备
4.排班表中配置了关联班次与关联班组
【4】1.节点为非末端节点
2.子节点部分关联排班方案
3.节点本身不关联官网设备
4.排班表中配置了关联班次与关联班组","【1】班次班组能耗值=关联表计的结束时间定时记录值-班次开始时间定时记录值
【2】班次班组能耗值=本身关联表计的班次结束时间定时记录值-班次开始时间定时记录值
【3】班次班组能耗值=Σ子节点班次班组能耗值
【4】班次班组能耗值=Σ关联排班方案子节点班次班组能耗值","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P1,
班次班组能源累计值计算是否正确,,班组排班方案 | 统计逻辑验证,已通过,陈树东,关联官网设备为管道房下方的管网设备,"【1】1.节点为末端节点
2.关联官网设备且有dataid=10000001测点
3.排班表中配置了关联班次与关联班组
【2】1.节点为非末端节点
2.子节点全部关联排班方案
3.节点本身关联官网设备且有dataid=10000001测点
4.排班表中配置了关联班次与关联班组
【3】1.节点为非末端节点
2.子节点全部关联排班方案
3.节点本身不关联官网设备
4.排班表中配置了关联班次与关联班组
【4】1.节点为非末端节点
2.子节点部分关联排班方案
3.节点本身不关联官网设备
4.排班表中配置了关联班次与关联班组","【1】班次班组能源累计值=关联表计的结束时间定时记录值-班次开始时间定时记录值
【2】班次班组能源累计值=本身关联表计的班次结束时间定时记录值-班次开始时间定时记录值
【3】班次班组能源累计值=Σ子节点班次班组能源累计值
【4】班次班组能源累计值=Σ关联排班方案子节点班次班组能源累计值","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P1,
班次班组能耗存表是否正确,,班组排班方案 | 统计逻辑验证,已通过,陈树东,存表teamgroupenergy,"【1】节点数据objectlabel、objectid
【2】logtime时标
【3】energytype
【4】aggregationCycle
【5】classid
【6】teamgroupid
【7】Value","【1】正确
【2】正确
【3】正确
【4】为12日
【5】正确
【6】正确
【7】正确","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致
【7】与预期一致",功能测试,P2,
月班次能耗值计算是否正确,,班组排班方案 | 统计逻辑验证,已通过,陈树东,管网设备为配电房下方管网设备,"【1】末端节点
【2】非末端节点","【1】月早班班次能耗值=Σ当班次时段能耗聚合值
【2】月早班班次能耗值=Σ当月早班能耗聚合值","【1】与预期一致
【2】与预期一致",功能测试,P1,
月班次能源累计值计算是否正确,,班组排班方案 | 统计逻辑验证,已通过,陈树东,管网设备为管道房下方管网设备,"【1】末端节点
【2】非末端节点","【1】月早班班次能源累计值=Σ当月早班时段能源累计值
【2】月早班班次能耗值=Σ当月早班能源累计值","【1】与预期一致
【2】与预期一致",功能测试,P1,
月班次能耗存表是否正确,,班组排班方案 | 统计逻辑验证,已通过,陈树东,存表teamgroupenergy,"【1】objectlabel、objectid
【2】logtime时标
【3】energytype
【4】aggregationCycle
【5】classid
【6】teamgroupid
【7】Value
【8】","【1】正确
【2】正确
【3】正确
【4】为14月
【5】正确
【6】为-1
【7】正确
【8】","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致
【7】与预期一致",功能测试,P2,
月班组能耗计算是否正确,,班组排班方案 | 统计逻辑验证,已通过,陈树东,管网设备为配电房下方的设备,"【1】末端节点
【2】非末端节点
【3】","【1】月班组能耗=Σ班组（生产1组）关联班次时段的能耗聚合值
【2】月班组能耗=Σ班组（生产1组）关联班次时段的能耗聚合值
【3】","【1】与预期一致
【2】与预期一致",功能测试,P2,
月班组能源累计值计算是否正确,,班组排班方案 | 统计逻辑验证,已通过,陈树东,管网设备为管道房下方的管网设备,"【1】末端节点
【2】非末端节点
【3】","【1】月班组能源累计值=Σ班组（生产1组）关联班次时段的能源累计值
【2】月班组能耗=Σ班组（生产1组）关联班次时段的能源累计值
【3】","【1】与预期一致
【2】与预期一致",功能测试,P2,
月班组能耗存表是否正确,,班组排班方案 | 统计逻辑验证,已通过,陈树东,存表teamgroupenergy,"【1】objectlabel、objectid
【2】logtime时标
【3】energytype
【4】aggregationCycle
【5】classid
【6】teamgroupid
【7】Value","【1】正确
【2】正确
【3】正确
【4】为14月
【5】为-1
【6】正确
【7】正确","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致
【7】与预期一致",功能测试,P2,
算法规则验证,,班组排班方案 | 统计逻辑验证,已通过,陈树东,,"【1】统计周期
【2】是否支持手动重算
【3】是否支持重算区间段设置","【1】班组能耗每个小时统计一次
【2】支持
【3】支持","【1】与预期一致
【2】与预期一致
【3】支持手动设置一个月",功能测试,P0,
数据新增更新逻辑验证,,班组排班方案 | 统计逻辑验证,已通过,陈树东,,"【1】首次计算
【2】手动重算
【3】","【1】teamgroupenergy表中新增一条数据记录；
【2】teamgroupenergy表中新增一条数据记录；
【3】","【1】与预期一致
【2】与预期一致",功能测试,P2,
单个表计聚合逻辑验证-正常,,班组排班方案 | 统计逻辑验证 | 场景,已通过,陈树东,节点下只存在单个表计，没有子节点,"【1】班次配置时间为8:00~16:00
【2】班次配置时间为昨日16:00~8:00
【3】班次配置时间为16:00~次日8:00","【1】班组班次能耗值=16:00定时记录值-8:00定时记录值；
结果与teamgroupenergy中的数据一致；
【2】班组班次能耗值=昨日16:00定时记录值-8:00定时记录值；
结果与teamgroupenergy中的数据一致；
【3】班组班次能耗值=次日8:00定时记录值-今日16:00定时记录值；
结果与teamgroupenergy中的数据一致；","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P0,
单个表计聚合逻辑验证-异常,,班组排班方案 | 统计逻辑验证 | 场景,已通过,陈树东,班次配置时间为8:00~16:00,"【1】8:00时刻还没有定时记录值
【2】16:00时刻没有定时记录值
【3】中间部分时刻的定时记录值不存在
【4】节点不关联表计
【5】16点的定时记录值小于8点的定时记录值
【6】16点的定时记录值远远大于8点的定时记录值","【1】会取8:05分的定时记录值进行计算；
结果与teamgroupenergy的数据一致
【2】会取15:55分的定时记录值进行计算；
结果与teamgroupenergy的数据一致
【3】15:55分的定时记录值-8:05分的定时记录值；
结果与teamgroupenergy的数据一致
【4】不存在班组能耗值；
teamgroupenergy中不存在该节点的数据记录

【5】16:00分的定时记录值-8:05分的定时记录值；
结果与teamgroupenergy的数据一致
【6】仍然可以正常计算","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致
【5】与预期一致
【6】与预期一致",功能测试,P1,
父子层级聚合逻辑验证-子节点全部关联排班方案（子节点下不存在孙节点）,,班组排班方案 | 统计逻辑验证 | 场景,已通过,陈树东,![Screenshot-1.png](https://tbfile.cet-electric.com:4753/thumbnail/013f77a19518ba242d11656c875d2be48d72/w/600/h/219 "600x219"),"【1】产线1关联了表计
【2】产线1不关联表计
设备1、设备2、设备3关联表计
【3】产线1、设备1、设备2、设备3都不关联了表计","【1】产线1的班组能耗=产线1关联表计的能耗值（考虑损耗情况下）；
结果与teamgroupenergy中的数据一致；
【2】产线1的班组能耗=设备1、设备2、设备3关联表计的能耗聚合值；
结果与teamgroupenergy中的数据一致；
【3】产线1没有能耗值；
teamgroupenergy没有该节点的数据记录","【1】与预期一致
【2】与预期一致
【3】与预期一致",功能测试,P0,
父子层级聚合逻辑验证-子节点部分关联排班方案（子节点下不存在孙节点）,,班组排班方案 | 统计逻辑验证 | 场景,已通过,陈树东,1.  ![Screenshot-3.png](https://tbfile.cet-electric.com:4753/thumbnail/013fc5c8c2efaa55caace1fd2df2fe51dd57/w/600/h/222 "600x222"),"【1】设备1、设备2、设备3都关联了表计
【2】设备1、设备2关联了表计，设备3不关联表计
【3】只有设备1或设备2关联了表计
【4】产线1、设备1、设备2都不关联表计
设备3关联了表计","【1】产线1的班组能耗=设备1与设备2的能耗聚合值；
结果与teamgroupenergy中的数据一致；
【2】产线1的班组能耗=设备1与设备2的能耗聚合值；
结果与teamgroupenergy中的数据一致；
【3】产线1的班组能耗=设备1或设备2的能耗聚合值；
结果与teamgroupenergy中的数据一致；
【4】产线1不存在能耗值；
teamgroupenergy没有产线1节点的数据记录","【1】与预期一致
【2】与预期一致
【3】与预期一致
【4】与预期一致",功能测试,P0,
修改关联设备,,班组排班方案 | 统计逻辑验证 | 场景,已通过,陈树东,原先该节点关联的是采集设备1,"【1】将采集设备1修改为采集设备2
【2】取消关联采集设备1","【1】进行重算时会使用采集设备2的定时记录进行计算，已存在的记录会进行更新
【2】进行重算时不会使用采集设备1的定时记录进行计算，已存在的记录会进行更新","【1】与预期一致
【2】与预期一致",功能测试,P1,
修改管理层级,,班组排班方案 | 统计逻辑验证 | 场景,已通过,陈树东,,"【1】删除部分层级时
【2】新增管理层级","【1】重算时不会新增该层级的数据记录
【2】计算时会新增该节点的数据记录","【1】与预期一致
【2】与预期一致",功能测试,P1,
修改排版方案中关联关系,,班组排班方案 | 统计逻辑验证 | 场景,已通过,陈树东,,"【1】将已关联的节点取消关联
【2】将未关联的节点进行关联","【1】进行重算时会屏蔽取消关联的节点数据
【2】进行重算时会增加新增关联的节点数据","【1】与预期一致
【2】与预期一致",功能测试,P2,
异常计算-数据中断如何处理,,班组排班方案 | 统计逻辑验证 | 场景,阻塞,陈树东,,"【1】""今日20240603，已计算到当前，停止job
今日20240605开启job""

【2】重算startime 20240101，计算至5月时停止job，不清除checkpoint
【3】重算startime 20240101，计算至5月时停止job，清除checkpoint
","【1】继续计算20240603至20240605数据（有定时记录的情况）

【2】从之前统计到的时标开始，继续计算至今

【3】从配置starttime20240101起，计算至今
","【1】
【2】不会继续计算
【3】不会继续计算",功能测试,P2,
