<template>
  <CetDialog class="CetDialog" v-bind="CetDialog_1">
    <div class="eem-cont-c1 fullwidth" style="padding-bottom: 1px; flex: 1">
      <CetForm
        ref="CetForm"
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
        class="flex-column"
        :key="visibleTrigger_in"
        style="height: calc(100% - 16px)"
      >
        <el-form-item label="班组名称" prop="name">
          <ElInput
            v-model.trim="CetForm_1.data.name"
            v-bind="ElInput_input_name"
            v-on="ElInput_input_name.event"
            maxlength="20"
          ></ElInput>
        </el-form-item>
        <el-form-item
          label="班组人数"
          prop=""
          v-if="schemeInfo.classTeamType === 2"
        >
          <el-input
            type="number"
            v-model.trim="CetForm_1.data.personCount"
            placeholder="请输入"
            oninput="value=value.replace(/^0+(\d)|[^\d]+/g,'')"
          >
            <template #suffix>人</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="班组成员"
          prop="personId"
          class="is-required"
          v-if="schemeInfo.classTeamType === 1"
        >
          <div style="height: 436px" class="flex">
            <div class="mrJ2 flex-column" style="width: 50%">
              <div class="head bg-BG2">{{ $T("选择成员") }}</div>
              <!-- left组件 -->
              <CetGiantTree
                :key="visibleTrigger_in"
                ref="tree"
                class="flex1 mtJ2"
                style="padding: 0 16px; overflow-x: auto"
                v-bind="CetGiantTree_left"
                v-on="CetGiantTree_left.event"
              ></CetGiantTree>
            </div>
            <div class="flex-column" style="width: 50%">
              <div class="head bg-BG2">{{ $T("已选成员") }}</div>
              <!-- right组件 -->
              <CetGiantTree
                ref="treeRight"
                class="flex1 mtJ2"
                style="padding: 0 16px; overflow-x: auto"
                v-bind="CetGiantTree_right"
                v-on="CetGiantTree_right.event"
              ></CetGiantTree>
            </div>
          </div>
        </el-form-item>
      </CetForm>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="mlJ1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
import common from "eem-utils/common";
import commonApi from "@/api/custom.js";
export default {
  name: "AddOrEditTeamList",
  props: {
    visibleTrigger_in: Number,
    closeTrigger_in: Number,
    inputData_in: Object,
    isAdd: Boolean,
    schemeInfo: Object,
    colorCurrentIndex: Number
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo;
    },
    projectTenantId() {
      var vm = this;
      return vm.$store.state.projectTenantId;
    }
  },
  data() {
    return {
      CetDialog_1: {
        showClose: true,
        width: "640px",
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime()
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          name: [
            {
              required: true,
              message: "请输入班组名称,且不超过20个字符",
              max: 20,
              trigger: ["blur"]
            }
          ]
        },
        event: { saveData_out: this.CetForm_1_saveData_out }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElInput_input_name: {
        value: "",
        placeholder: "请输入",
        style: {
          width: "100%"
        },
        event: {}
      },
      currenNodeList: [],
      CetGiantTree_right: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        customSearch: common.fuzzySearch, //自定义关键字搜索方法
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              title: "text",
              name: "nameAndNicName" //节点名称
            }
          }
        },
        searchText_in: "",
        unCheckTrigger_in: Date.now(),
        event: {}
      },
      // left组件
      CetGiantTree_left: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        customSearch: common.fuzzySearch, //自定义关键字搜索方法
        setting: {
          check: {
            enable: true //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            },
            key: {
              title: "text",
              name: "nameAndNicName" //节点名称
            }
          }
        },
        searchText_in: "",
        unCheckTrigger_in: Date.now(),
        event: {
          checkedNodes_out: this.CetGiantTree_left_checkedNodes_out //勾选节点输出
        }
      }
    };
  },
  watch: {
    async visibleTrigger_in(val) {
      if (this.schemeInfo.classTeamType === 1) this.getTreeData();
      this.CetDialog_1.title = `${this.isAdd ? "新增" : "修改"}班组`;
      this.CetDialog_1.width =
        this.schemeInfo.classTeamType === 1 ? "640px" : "320px";
      this.CetDialog_1.openTrigger_in = val;
      this.CetForm_1.data = this.isAdd ? {} : this.inputData_in;
      if (this.inputData_in?.personId) {
        const list = JSON.parse(this.inputData_in.personId);
        this.CetGiantTree_left.checkedNodes = list.map(res => ({
          tree_id: `user_${res}`
        }));
      }
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
      this.currenNodeList = [];
      this.CetGiantTree_left.checkedNodes = [];
      this.CetGiantTree_left.inputData_in = [];
      this.CetGiantTree_right.inputData_in = [];
    },
    "CetDialog_1.closeTrigger_in"(val) {
      this.currenNodeList = [];
      this.CetGiantTree_left.checkedNodes = [];
      this.CetGiantTree_left.inputData_in = [];
      this.CetGiantTree_right.inputData_in = [];
    }
  },
  methods: {
    /**
     * 弹窗确认按钮
     */
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },

    /**
     * 弹窗取消按钮
     */
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },

    /**
     *  获取用户组列表信息
     */
    getTreeData() {
      let data = {
        loadChildren: true,
        removeRootUser: true,
        tenantId: this.projectTenantId
      };
      commonApi.queryProjectUserAndGroup(data).then(res => {
        const treeData = this._.get(res, "data", []) || [];
        this.combinationName(treeData);
        this.CetGiantTree_left.selectNode = this._.get(treeData, "[0]", null);
        this.CetGiantTree_left.inputData_in = this._.cloneDeep(treeData);
        if (!this.isAdd) {
          this.currenNodeList = this.$refs.tree.AllCheckedNode();
        }
      });
    },
    /**
     *  名称与昵称组合
     */
    combinationName(data) {
      if (!data?.length) return;
      data.forEach(item => {
        if (item.modelLabel === "user" && item.nicName) {
          item.nameAndNicName = `${item.nicName}(${item.name})`;
        } else {
          item.nameAndNicName = item.name;
        }
        if (item?.children?.length) {
          this.combinationName(item.children);
        }
      });
    },

    /**
     *  勾选节点树
     */
    CetGiantTree_left_checkedNodes_out(val) {
      const nodes = this.CetGiantTree_left.searchText_in
        ? this.$refs.tree.AllCheckedNode()
        : val;
      function filterUserNodes(nodes) {
        return nodes.filter(node => {
          if (node.modelLabel === "user") {
            if (node.children) {
              node.children = filterUserNodes(node.children);
            }
            return true;
          }
          return false;
        });
      }
      const userList = filterUserNodes(nodes);
      const checkedNodes = userList;
      this.currenNodeList = checkedNodes;
      this.CetGiantTree_right.inputData_in = this._.cloneDeep(checkedNodes);
    },

    /**
     * 表单保存
     */
    async CetForm_1_saveData_out() {
      const personId = this.currenNodeList.map(item => {
        return item.id;
      });
      if (!personId.length && this.schemeInfo.classTeamType === 1) {
        return this.$message.warning("请选择班组成员");
      }
      const saveData = {
        color: "oil" + (Number(this.colorCurrentIndex) + 1),
        id: this.inputData_in?.id || null,
        name: this.CetForm_1.data.name,
        personCount: this.CetForm_1.data.personCount,
        personId: personId ? JSON.stringify(personId) : "",
        schedulingSchemeId: this.schemeInfo.id
      };
      const res = await commonApi.addOrUpdateTeamgroupinfo(saveData);
      if (res.code !== 0) return;
      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.$emit("save_out");
      this.CetDialog_1.closeTrigger_in = Date.now();
    }
  }
};
</script>

<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  :deep(.el-dialog__body) {
    display: flex;
  }
  :deep(.el-form-item) {
    .el-form-item__label {
      line-height: 24px;
      text-align: left;
    }
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
  .treeBox {
    width: 50%;
    overflow-x: auto;
  }
  .head {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid;
    @include border_color(B1);
    padding: 0 16px;
    font-weight: bold;
  }
  :deep(.el-table .cell) {
    padding-left: 16px;
    padding-right: 16px;
  }
  ::v-deep .ztree {
    height: calc(100% - 45px) !important;
  }

  ::v-deep input::-webkit-outer-spin-button,
  ::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
  ::v-deep input[type="number"] {
    -moz-appearance: textfield;
  }

  ::v-deep input::-webkit-outer-spin-button,
  ::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
  ::v-deep input[type="number"] {
    -moz-appearance: textfield;
  }
}
</style>
