"""
班组排班方案 - 排班方案 - 新增功能测试脚本
包含前5个测试用例的自动化测试
"""

import pytest
from playwright.sync_api import Page, expect


def test_01_new_dialog_interface_verification(page: Page) -> None:
    """
    测试用例1: 新建弹窗界面验证
    所属分组: 班组排班方案 | 排班方案 | 新增
    步骤描述: 【1】页面空间分配 【2】按钮形状 【3】各部分颜色
    预期结果: 【1】与原型一致 【2】与原型一致 【3】与原型一致
    """
    # 登录系统
    page.goto("http://10.12.140.38:8090/#/login")
    page.get_by_placeholder("请输入账号").fill("zpl")
    page.get_by_placeholder("请输入密码").fill("Cet12345!")
    page.get_by_role("button", name="登录").click()
    page.wait_for_load_state("networkidle")

    # 导航到排班方案页面
    page.goto("http://10.12.140.38:8090/#/schedulingPlan")
    page.wait_for_load_state("networkidle")

    # 【步骤1】点击新增排班方案按钮
    add_button = page.get_by_text("新增排班方案")
    expect(add_button).to_be_visible()
    expect(add_button).to_be_enabled()
    add_button.click()

    # 等待弹窗出现
    page.wait_for_selector("text=新增排班方案", state="visible")
    dialog_area = page.locator('.el-dialog').filter(has_text="新增排班方案")
    expect(dialog_area).to_be_visible()

    # 【验证1】页面空间分配 - 验证弹窗布局和尺寸
    # 验证弹窗标题
    dialog_title = dialog_area.locator(".el-dialog__title")
    expect(dialog_title).to_be_visible()
    expect(dialog_title).to_contain_text("新增排班方案")

    # 验证弹窗主体内容区域
    dialog_body = dialog_area.locator(".el-dialog__body")
    expect(dialog_body).to_be_visible()

    # 验证弹窗底部按钮区域
    dialog_footer = dialog_area.locator(".el-dialog__footer")
    expect(dialog_footer).to_be_visible()

    # 【验证2】按钮形状 - 验证按钮存在且可见
    confirm_button = dialog_area.get_by_role("button", name="确定")
    cancel_button = dialog_area.get_by_role("button", name="取消")
    expect(confirm_button).to_be_visible()
    expect(confirm_button).to_be_enabled()
    expect(cancel_button).to_be_visible()
    expect(cancel_button).to_be_enabled()

    # 【验证3】各部分颜色 - 验证表单元素正确显示
    # 验证表单标签
    name_label = dialog_area.locator("label").filter(has_text="排班方案名称")
    type_label = dialog_area.locator("label").filter(has_text="班组类型")
    expect(name_label).to_be_visible()
    expect(type_label).to_be_visible()

    # 验证输入框
    name_input = dialog_area.get_by_placeholder("请输入")
    expect(name_input).to_be_visible()
    expect(name_input).to_be_enabled()

    # 验证单选按钮组
    radio_group = dialog_area.locator(".el-radio-group")
    expect(radio_group).to_be_visible()

    # 验证单选按钮选项
    maintenance_radio = dialog_area.locator(".el-radio").filter(has_text="运维班组")
    production_radio = dialog_area.locator(".el-radio").filter(has_text="生产班组")
    expect(maintenance_radio).to_be_visible()
    expect(production_radio).to_be_visible()

    # 验证默认选中状态（运维班组应该默认选中）
    maintenance_input = maintenance_radio.locator("input[type='radio']")
    production_input = production_radio.locator("input[type='radio']")
    expect(maintenance_input).to_be_checked()
    expect(production_input).not_to_be_checked()

    # 关闭弹窗
    cancel_button.click()

    # 验证弹窗已关闭
    expect(dialog_area).not_to_be_visible()


def test_02_add_button_effectiveness(page: Page) -> None:
    """
    测试用例2: 新增按钮是否有效
    所属分组: 班组排班方案 | 排班方案 | 新增
    步骤描述: 【1】点击新增排版方案
    预期结果: 【1】弹出新增排版方案弹窗
    """
    # 登录系统
    page.goto("http://10.12.140.38:8090/#/login")
    page.get_by_placeholder("请输入账号").fill("zpl")
    page.get_by_placeholder("请输入密码").fill("Cet12345!")
    page.get_by_role("button", name="登录").click()
    page.wait_for_load_state("networkidle")

    # 导航到排班方案页面
    page.goto("http://10.12.140.38:8090/#/schedulingPlan")
    page.wait_for_load_state("networkidle")

    # 【步骤1】点击新增排班方案按钮
    add_button = page.get_by_text("新增排班方案")
    expect(add_button).to_be_visible()
    expect(add_button).to_be_enabled()
    add_button.click()

    # 【验证1】弹出新增排班方案弹窗
    page.wait_for_selector("text=新增排班方案", state="visible")
    dialog_area = page.locator('.el-dialog').filter(has_text="新增排班方案")
    expect(dialog_area).to_be_visible()

    # 验证弹窗标题
    dialog_title = dialog_area.locator(".el-dialog__title")
    expect(dialog_title).to_contain_text("新增排班方案")

    # 关闭弹窗
    cancel_button = dialog_area.get_by_role("button", name="取消")
    cancel_button.click()


def test_03_dialog_default_values_verification(page: Page) -> None:
    """
    测试用例3: 弹窗属性默认值校验
    所属分组: 班组排班方案 | 排班方案 | 新增
    步骤描述: 【1】进入新增排版方案弹窗
    预期结果: 【1】排版方案名称为空，班组类型默认选择第一个班组
    """
    # 登录系统
    page.goto("http://10.12.140.38:8090/#/login")
    page.get_by_placeholder("请输入账号").fill("zpl")
    page.get_by_placeholder("请输入密码").fill("Cet12345!")
    page.get_by_role("button", name="登录").click()
    page.wait_for_load_state("networkidle")

    # 导航到排班方案页面
    page.goto("http://10.12.140.38:8090/#/schedulingPlan")
    page.wait_for_load_state("networkidle")

    # 【步骤1】进入新增排班方案弹窗
    add_button = page.get_by_text("新增排班方案")
    add_button.click()

    # 等待弹窗出现
    page.wait_for_selector("text=新增排班方案", state="visible")
    dialog_area = page.locator('.el-dialog').filter(has_text="新增排班方案")
    expect(dialog_area).to_be_visible()

    # 【验证1】排班方案名称为空
    name_input = dialog_area.get_by_placeholder("请输入")
    expect(name_input).to_have_value("")

    # 【验证1】班组类型默认选择第一个班组（运维班组）
    maintenance_radio = dialog_area.locator(".el-radio").filter(has_text="运维班组")
    production_radio = dialog_area.locator(".el-radio").filter(has_text="生产班组")
    
    maintenance_input = maintenance_radio.locator("input[type='radio']")
    production_input = production_radio.locator("input[type='radio']")
    
    expect(maintenance_input).to_be_checked()
    expect(production_input).not_to_be_checked()

    # 关闭弹窗
    cancel_button = dialog_area.get_by_role("button", name="取消")
    cancel_button.click()


def test_04_plan_name_length_validation(page: Page) -> None:
    """
    测试用例4: 排班方案名称长度校验
    所属分组: 班组排班方案 | 排班方案 | 新增
    前置条件: 进入新增排版方案弹窗，名称长度范围为1~20
    步骤描述: 【1】排版方案名称中输入20个字符 【2】排版方案名称中输入一个字符 
             【3】排版方案名称中输入21个字符 【4】排版方案名称中不输入字符
    预期结果: 【1】保存成功，排版方案的名称为输入的内容 【2】保存成功，排版方案的名称为输入的内容
             【3】保存失败，给出字符超出界限的提示 【4】保存失败，给出字符超出界限的提示
    """
    # 登录系统
    page.goto("http://10.12.140.38:8090/#/login")
    page.get_by_placeholder("请输入账号").fill("zpl")
    page.get_by_placeholder("请输入密码").fill("Cet12345!")
    page.get_by_role("button", name="登录").click()
    page.wait_for_load_state("networkidle")

    # 导航到排班方案页面
    page.goto("http://10.12.140.38:8090/#/schedulingPlan")
    page.wait_for_load_state("networkidle")

    # 进入新增排班方案弹窗
    add_button = page.get_by_text("新增排班方案")
    add_button.click()

    page.wait_for_selector("text=新增排班方案", state="visible")
    dialog_area = page.locator('.el-dialog').filter(has_text="新增排班方案")
    expect(dialog_area).to_be_visible()

    name_input = dialog_area.get_by_placeholder("请输入")
    confirm_button = dialog_area.get_by_role("button", name="确定")
    cancel_button = dialog_area.get_by_role("button", name="取消")

    # 【步骤1】排班方案名称中输入20个字符
    twenty_chars = "测试排班方案名称十二三四五六七八九十"  # 20个字符
    name_input.clear()
    name_input.fill(twenty_chars)
    confirm_button.click()
    
    # 等待保存结果，验证是否成功保存
    page.wait_for_timeout(2000)
    # 如果保存成功，弹窗应该关闭
    if not dialog_area.is_visible():
        # 保存成功，验证列表中是否有新增的排班方案
        plan_list = page.locator(".scheduling-plan-list")
        expect(plan_list).to_contain_text(twenty_chars)
    else:
        # 如果弹窗仍然存在，说明保存失败，关闭弹窗继续测试
        cancel_button.click()

    # 重新打开弹窗进行下一个测试
    add_button.click()
    page.wait_for_selector("text=新增排班方案", state="visible")
    dialog_area = page.locator('.el-dialog').filter(has_text="新增排班方案")

    # 【步骤2】排班方案名称中输入一个字符
    one_char = "测"
    name_input = dialog_area.get_by_placeholder("请输入")
    confirm_button = dialog_area.get_by_role("button", name="确定")
    cancel_button = dialog_area.get_by_role("button", name="取消")
    
    name_input.clear()
    name_input.fill(one_char)
    confirm_button.click()
    
    # 等待保存结果
    page.wait_for_timeout(2000)
    if not dialog_area.is_visible():
        # 保存成功，验证列表中是否有新增的排班方案
        plan_list = page.locator(".scheduling-plan-list")
        expect(plan_list).to_contain_text(one_char)
    else:
        cancel_button.click()

    # 重新打开弹窗进行下一个测试
    add_button.click()
    page.wait_for_selector("text=新增排班方案", state="visible")
    dialog_area = page.locator('.el-dialog').filter(has_text="新增排班方案")

    # 【步骤3】排班方案名称中输入21个字符
    twenty_one_chars = "测试排班方案名称十二三四五六七八九十一"  # 21个字符
    name_input = dialog_area.get_by_placeholder("请输入")
    confirm_button = dialog_area.get_by_role("button", name="确定")
    cancel_button = dialog_area.get_by_role("button", name="取消")
    
    name_input.clear()
    name_input.fill(twenty_one_chars)
    
    # 验证输入框是否只保留前20个字符
    input_value = name_input.input_value()
    assert len(input_value) <= 20, f"输入框应该只保留前20个字符，实际长度: {len(input_value)}"
    
    confirm_button.click()
    
    # 等待保存结果
    page.wait_for_timeout(2000)
    # 验证是否有错误提示或者输入被截断
    error_message = page.locator(".el-message--error")
    if error_message.is_visible():
        # 有错误提示，验证提示内容
        expect(error_message).to_contain_text("字符")
    
    cancel_button.click()

    # 重新打开弹窗进行最后一个测试
    add_button.click()
    page.wait_for_selector("text=新增排班方案", state="visible")
    dialog_area = page.locator('.el-dialog').filter(has_text="新增排班方案")

    # 【步骤4】排班方案名称中不输入字符
    name_input = dialog_area.get_by_placeholder("请输入")
    confirm_button = dialog_area.get_by_role("button", name="确定")
    cancel_button = dialog_area.get_by_role("button", name="取消")
    
    name_input.clear()  # 确保输入框为空
    confirm_button.click()
    
    # 等待保存结果，应该有错误提示
    page.wait_for_timeout(2000)
    error_message = page.locator(".el-message--error")
    if error_message.is_visible():
        # 验证错误提示内容
        expect(error_message).to_contain_text("不能为空")
    
    # 关闭弹窗
    cancel_button.click()


def test_05_plan_name_character_validation(page: Page) -> None:
    """
    测试用例5: 排版方案名称任意字符校验
    所属分组: 班组排班方案 | 排班方案 | 新增
    前置条件: 进入新增排版方案弹窗
    步骤描述: 【1】输入数字、特殊字符、中/英文
    预期结果: 【1】均支持输入保存
    """
    # 登录系统
    page.goto("http://10.12.140.38:8090/#/login")
    page.get_by_placeholder("请输入账号").fill("zpl")
    page.get_by_placeholder("请输入密码").fill("Cet12345!")
    page.get_by_role("button", name="登录").click()
    page.wait_for_load_state("networkidle")

    # 导航到排班方案页面
    page.goto("http://10.12.140.38:8090/#/schedulingPlan")
    page.wait_for_load_state("networkidle")

    # 进入新增排班方案弹窗
    add_button = page.get_by_text("新增排班方案")
    add_button.click()

    page.wait_for_selector("text=新增排班方案", state="visible")
    dialog_area = page.locator('.el-dialog').filter(has_text="新增排班方案")
    expect(dialog_area).to_be_visible()

    # 【步骤1】输入数字、特殊字符、中/英文
    mixed_chars = "测试Plan123!@#"  # 包含中文、英文、数字、特殊字符
    name_input = dialog_area.get_by_placeholder("请输入")
    confirm_button = dialog_area.get_by_role("button", name="确定")
    cancel_button = dialog_area.get_by_role("button", name="取消")
    
    name_input.clear()
    name_input.fill(mixed_chars)
    
    # 验证输入框能够正确显示所有字符
    input_value = name_input.input_value()
    expect(input_value).to_contain(mixed_chars)
    
    confirm_button.click()
    
    # 等待保存结果
    page.wait_for_timeout(2000)
    
    # 【验证1】均支持输入保存
    if not dialog_area.is_visible():
        # 保存成功，验证列表中是否有新增的排班方案
        plan_list = page.locator(".scheduling-plan-list")
        expect(plan_list).to_contain_text(mixed_chars)
    else:
        # 如果弹窗仍然存在，检查是否有错误提示
        error_message = page.locator(".el-message--error")
        if error_message.is_visible():
            # 如果有错误提示，说明某些字符不被支持
            print(f"错误提示: {error_message.text_content()}")
        
        # 关闭弹窗
        cancel_button.click()
