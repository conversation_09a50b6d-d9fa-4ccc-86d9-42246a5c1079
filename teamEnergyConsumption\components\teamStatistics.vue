<template>
  <div class="teamBox mtJ2">
    <div class="header">
      <span class="title">班次对比</span>
      <omega-icon class="tipsIcon icon-size-I0" symbolId="attention" />
      <span class="text">
        班次对比支持根据班组、班次相关条件进行筛选，默认展示第一个班次方案的班次数据，最多支持4个班次的对比
      </span>
    </div>
    <div class="content flex" v-if="hasEnergyData">
      <div
        class="teamList"
        v-for="(res, teamIndex) in energyStatisticList"
        :key="teamIndex"
      >
        <div
          class="teamItem"
          v-for="(item, classIndex) in res.classesNames"
          :key="`${teamIndex}-${classIndex}`"
        >
          <div>
            <span class="teamName">{{ formatterText(item.configName) }}</span>
            <span class="configName mtJ1">
              {{ `(${formatterText(item.schemeName)})` }}
            </span>
          </div>
        </div>
        <div class="compareList mtJ2">
          <div class="compareName">{{ formatterText(res.teamGroupNames) }}</div>
          <div class="compareName">
            用能占比
            <span class="value">{{ formatterText(res.energyProportion) }}</span>
            %
          </div>
          <div class="compareName">
            {{ energyTypeInfo.name ? `用${energyTypeInfo.name}量` : "" }}
            <span class="value">{{ formatterText(res.energyTotal) }}</span>
            {{ res.energyUnit }}
          </div>
          <div class="compareName">
            平均班次能耗
            <span class="value">{{ formatterText(res.avgEnergy) }}</span>
            {{ res.energyUnit }}
          </div>
        </div>
        <el-popover
          placement="bottom"
          width="408"
          trigger="click"
          @show="popoverShowChange(teamIndex)"
          @hide="popoverHideChange(teamIndex)"
        >
          <div
            class="flex"
            v-if="schedulingPlanInfo.id"
            style="padding: 4px 0 4px 12px !important"
          >
            <div class="leftContainer">
              <div class="title mbJ1">生产班组</div>
              <el-checkbox-group class="group" v-model="selectedTeamGroups">
                <el-checkbox
                  v-for="team in schedulingPlanInfo.teamGroupInfoList"
                  :key="team.id"
                  :label="team.id"
                  style="margin-bottom: 8px; margin-right: 16px"
                >
                  <TextTooltip
                    style="
                      width: 100px;
                      display: inline-block;
                      vertical-align: -4px;
                    "
                    placement="top"
                    :content="team.name"
                    :text="team.name"
                  ></TextTooltip>
                </el-checkbox>
              </el-checkbox-group>
            </div>
            <div class="rightContainer plJ2">
              <div
                v-for="(scheme, index) in schedulingPlanInfo.classesSchemeList"
                :key="index"
              >
                <div class="mbJ1">{{ scheme.name }}</div>
                <el-checkbox-group
                  v-model="selectedClasses"
                  class="flex mbJ1 checkboxGroup"
                  v-if="scheme?.classesConfigList?.length"
                >
                  <el-checkbox
                    v-for="config in scheme.classesConfigList || []"
                    :key="config.id"
                    :label="config.id"
                    class="mtJ1"
                    style="margin-right: 10px"
                  >
                    <TextTooltip
                      style="
                        width: 42px;
                        display: inline-block;
                        vertical-align: -4px;
                      "
                      placement="top"
                      :content="config.name"
                      :text="config.name"
                    ></TextTooltip>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
          <img slot="reference" :src="setImg" class="setImg" />
        </el-popover>
      </div>
    </div>
    <div v-else class="no-data">暂无数据</div>
  </div>
</template>

<script>
import TextTooltip from "../../components/textTooltip.vue";
export default {
  name: "teamStatistics",
  props: {
    energyTypeInfo: {
      type: Object,
      default: () => {}
    },
    energyStatisticList: {
      type: Array,
      default: () => []
    },
    schedulingPlanInfo: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    TextTooltip
  },
  computed: {
    // 新增计算属性，提高代码可读性
    hasEnergyData() {
      return this.energyStatisticList?.length > 0;
    }
  },
  data() {
    return {
      setImg: require("../../assets/setting.png"),
      selectedTeamGroups: [], // 新增：用于存储选中的班组
      selectedClasses: [] // 新增：用于存储选中的班次
    };
  },
  methods: {
    /**
     *  无数据处理
     */
    formatterText(val) {
      return val || val == 0 ? val : "--";
    },

    /**
     *  popover隐藏事件
     */
    popoverHideChange(index) {
      const selectedTeamGroup = this.selectedTeamGroups;
      const selectedClass = this.selectedClasses.flat();
      if (!selectedTeamGroup?.length && !selectedClass?.length) return;
      this.$emit("selectedDataChanged", {
        selectedTeamGroup,
        selectedClass,
        index
      });
      this.selectedTeamGroups = [];
      this.selectedClasses = [];
    },

    /**
     *  popover显示事件
     */
    popoverShowChange(index) {
      const rowData = this.energyStatisticList[index];
      const { teamGroupIdList = [], classesConfigIdList = [] } = rowData;
      this.selectedTeamGroups = teamGroupIdList || [];
      this.selectedClasses = classesConfigIdList || [];
    }
  },
  mounted() {}
};
</script>

<style lang="scss" scoped>
/* 样式部分保持不变 */
.oil3 {
  @include themeify {
    background: rgba(themed(oil3), 0.1);
  }
}
.teamBox {
  width: 100%;
  max-height: 235px;
  min-height: 209px;
  height: 100%;
  padding: 16px 24px;
  box-sizing: border-box;
  @include background_color(BG2);
  .header {
    .title {
      font-size: 16px;
      @include font_color(T1);
      font-weight: 500;
    }
    .tipsIcon {
      margin-left: 13px;
      margin-right: 8px;
      @include font_color(B1);
      vertical-align: -2px;
    }
    .text {
      font-size: 12px;
      @include font_color(T3);
    }
  }
  .content {
    display: flex;
    margin-top: 16px;
    justify-content: flex-start;
    .teamList {
      width: 25%;
      height: 100%;
      margin-right: 16px;
      border-right: 1px solid;
      position: relative;
      @include border_color(BG3);
      &:last-child {
        margin-right: 0;
      }
      .teamItem {
        margin-bottom: 8px;
        .teamName {
          font-size: 14px;
          font-weight: 500;
          @include font_color(T1);
        }
        .configName {
          font-size: 12px;
          @include font_color(T3);
        }
      }
      .compareList {
        border-radius: 4px;
        .compareName {
          font-size: 12px;
          @include font_color(T3);
          margin-bottom: 4px;
          &:last-child {
            margin-bottom: 0;
          }
        }
        .value {
          font-family: Barlow, Barlow;
          font-size: 16px;
          @include font_color(T1);
          vertical-align: -1px;
        }
      }
      .setImg {
        position: absolute;
        top: 4px;
        right: 12px;
      }
    }
  }
  .no-data {
    text-align: center;
    line-height: 110px;
    @include font_color(T4);
    padding: 20px;
  }
}
.leftContainer {
  width: 40%;
  border-right: 1px solid #ccc;
  .group {
    display: flex;
    flex-direction: column;
  }
}
.rightContainer {
  width: 60%;
  .checkboxGroup {
    flex-wrap: wrap;
  }
}
</style>
