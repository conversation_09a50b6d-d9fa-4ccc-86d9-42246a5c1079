<template>
  <div class="teamList">
    <div>
      <CetButton
        class="mtJ2 mbJ2"
        v-bind="CetButton_add"
        v-on="CetButton_add.event"
      ></CetButton>
    </div>
    <div class="cardBox" v-if="teamList.length">
      <div class="itemCard" v-for="(item, index) in teamList" :key="index">
        <div class="flex items-center">
          <div class="flex-1 title">
            <div
              :style="{
                width: '12px',
                height: '12px',
                display: 'inline-block',
                verticalAlign: '-1px'
              }"
              :class="item.color"
            ></div>
            {{ item.name }}
          </div>
          <div class="flex-1 text-right items-center">
            <omega-icon
              class="editIcon"
              symbolId="edit-hol"
              @click="editHandleTeamPlan(item, index)"
            ></omega-icon>
            <omega-icon
              class="delIcon"
              symbolId="delete-lin"
              @click="deleteHandleTeamPlan(item)"
            ></omega-icon>
          </div>
        </div>
        <div class="flex items-center mtJ2">
          <div class="flex-1 classesBox" v-if="schemeInfo.classTeamType === 1">
            <span class="classesName">班组成员</span>
            <span class="time">
              {{ item.personName || "--" }}
            </span>
          </div>
          <div class="flex-1 classesBox" v-else>
            <span class="classesName">班组人数</span>
            <span class="time">
              {{ formatterText(item.personCount) }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="emptyData" v-else>
      <img :src="emptyDataImg" />
      <div class="emptyData">列表暂无数据</div>
    </div>
    <AddOrEditTeamList
      :schemeInfo="schemeInfo"
      :colorCurrentIndex="colorCurrentIndex"
      v-bind="addOrEditShiftPlan"
      @save_out="getQueryTeamgroupinfo"
    />
  </div>
</template>

<script>
import commonApi from "@/api/custom.js";
import AddOrEditTeamList from "../dialog/AddOrEditTeamList.vue";
export default {
  name: "teamList",
  components: { AddOrEditTeamList },
  props: {
    schemeInfo: {
      type: Object,
      default: () => {}
    },
    tableList: {
      type: Array,
      default: () => []
    },
    refreshTrigger_in: {
      type: Number
    }
  },
  data() {
    return {
      CetButton_add: {
        class: "fl",
        visible_in: true,
        disable_in: false,
        title: "新增班组",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_teamPlan_out
        }
      },
      addOrEditShiftPlan: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        isAdd: null
      },
      teamList: [],
      emptyDataImg: require("../../../assets/empty.png"),
      colorCurrentIndex: 0,
      isFirstLoad: true, // 标记是否是首次加载
      shouldRefreshParent: false // 标记是否所有数据都被删除了
    };
  },
  watch: {
    schemeInfo: {
      handler(newVal) {
        if (newVal.id) {
          this.getQueryTeamgroupinfo();
        }
      },
      immediate: true,
      deep: true
    },
    refreshTrigger_in: {
      deep: true,
      handler(newVal) {
        this.CetButton_add.disable_in = this.tableList.length === 0;
      }
    }
  },
  methods: {
    /**
     *  无数据处理
     */
    formatterText(val) {
      return val || val == 0 ? val : "--";
    },

    /*
     * 查询班组方案
     */
    async getQueryTeamgroupinfo() {
      let params = {
        schedulingSchemeId: this.schemeInfo.id
      };
      let res = await commonApi.queryTeamgroupinfo(params);
      if (res.code === 0) {
        this.teamList = _.get(res, "data", []);
        if (this.shouldRefreshParent && !this.isFirstLoad) {
          this.$emit("refreshTable");
          this.shouldRefreshParent = false; // 重置标志位
        }
        if (this.isFirstLoad) {
          this.isFirstLoad = false;
        }
      }
    },

    /*
     * 新增班次方案按钮
     */
    CetButton_add_teamPlan_out() {
      if (!this.tableList?.length) return;
      if (this.teamList.length > 9) {
        this.colorCurrentIndex = 0;
        return this.$message({
          message: $T("最多添加10个班组方案"),
          type: "warning"
        });
      }
      this.colorCurrentIndex = this.getAvailableColorIndex();
      this.addOrEditShiftPlan.inputData_in = null;
      this.shouldRefreshParent = true;
      this.addOrEditShiftPlan.isAdd = true;
      this.addOrEditShiftPlan.visibleTrigger_in = +new Date();
    },

    /*
     * 编辑班次方案按钮
     */
    editHandleTeamPlan(row, index) {
      this.addOrEditShiftPlan.inputData_in = row;
      this.addOrEditShiftPlan.isAdd = false;
      this.addOrEditShiftPlan.visibleTrigger_in = +new Date();
      this.colorCurrentIndex = index;
    },

    /*
     * 删除班次方案按钮
     */
    deleteHandleTeamPlan(row) {
      this.$confirm("确定要删除所选项吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          const res = await commonApi.deleteTeamgroupinfo({ id: row.id });
          if (res.code !== 0) return;
          this.$message({
            message: $T("删除成功"),
            type: "success"
          });
          this.colorCurrentIndex = this.getAvailableColorIndex();
          this.shouldRefreshParent = true;
          await this.getQueryTeamgroupinfo();
        })
        .catch(() => {});
    },

    /*
     * 获取可用的颜色索引
     */
    getAvailableColorIndex() {
      const usedColors = this.teamList.map(item => item.color);
      for (let i = 0; i < 10; i++) {
        const color = `oil${i + 1}`;
        if (!usedColors.includes(color)) {
          return i;
        }
      }
      return 0; // 如果没有可用索引，默认返回 0
    }
  }
};
</script>

<style lang="scss" scoped>
.teamList {
  display: flex;
  flex-direction: column;
  .cardBox {
    .itemCard {
      padding: 24px;
      box-sizing: border-box;
      border: 1px solid;
      @include border-color(B2);
      border-radius: 4px;
      margin-bottom: 16px;
      .title {
        font-weight: 500;
      }
      .editIcon,
      .delIcon {
        cursor: pointer;
        margin-left: 10px;
        font-size: 20px;
      }
      .editIcon {
        @include font_color(T3);
      }
      .delIcon {
        @include font_color(Sta3);
      }
      .classesBox {
        .classesName {
          font-weight: 500;
          @include font_color(T2);
          margin-right: 10px;
        }
        .time {
          @include font_color(T3);
        }
      }
    }
  }
  .emptyData {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 50%;
    transform: translateY(-34%);
    img {
      width: 200px;
      height: 200px;
    }
    div {
      margin-top: 20px;
      @include font_color(T3);
    }
  }
}
@import "../../../assets/bgc.scss";
</style>
