<template>
  <div class="page eem-common">
    <el-container class="fullheight padding0">
      <el-aside width="315px" class="eem-aside flex-column">
        <customElSelect
          class="mbJ1"
          v-model="ElSelect_schedulingPlan.value"
          v-bind="ElSelect_schedulingPlan"
          v-on="ElSelect_schedulingPlan.event"
          :prefix_in="$T('排班方案')"
        >
          <ElOption
            v-for="item in ElOptions_schedulingPlan.options_in"
            :key="item[ElOptions_schedulingPlan.key]"
            :label="item[ElOptions_schedulingPlan.label]"
            :value="item[ElOptions_schedulingPlan.value]"
            :disabled="item[ElOptions_schedulingPlan.disabled]"
          ></ElOption>
        </customElSelect>
        <customElSelect
          class="mbJ1"
          v-model="ElSelect_energyType.value"
          v-bind="ElSelect_energyType"
          v-on="ElSelect_energyType.event"
          :prefix_in="$T('能源类型')"
        >
          <ElOption
            v-for="item in ElOptions_energyType.options_in"
            :key="item[ElOptions_energyType.key]"
            :label="item[ElOptions_energyType.label]"
            :value="item[ElOptions_energyType.value]"
            :disabled="item[ElOptions_energyType.disabled]"
          ></ElOption>
        </customElSelect>
        <CetTree
          class="flex-auto"
          ref="cetTree"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        >
          <span
            class="custom-tree-node el-tree-node__label"
            slot-scope="{ node }"
            :level="node.level"
          >
            <span
              :style="
                disabledNodeColor(node, 'data.childSelectState', 1, '#989898')
              "
            >
              {{ node.label }}
            </span>
          </span>
        </CetTree>
        <!-- <CetGiantTree
          class="flex-auto"
          ref="cetTree"
          :selectNode.sync="CetTree_1.selectNode"
          :checkedNodes.sync="CetTree_1.checkedNodes"
          v-bind="CetTree_1"
          v-on="CetTree_1.event"
        ></CetGiantTree> -->
      </el-aside>
      <el-main class="fullheight padding0 mlJ3">
        <el-container
          class="fullheight flex-column eem-min-width"
          style="height: 100%"
        >
          <div class="eem-container">
            <div class="header">
              <div class="mrJ1 fl">
                <customElSelect
                  v-model="ElSelect_dateType.value"
                  v-bind="ElSelect_dateType"
                  v-on="ElSelect_dateType.event"
                  class="mrJ1 fl"
                  :prefix_in="$T('分析周期')"
                >
                  <ElOption
                    v-for="item in ElOption_dateType.options_in"
                    :key="item[ElOption_dateType.key]"
                    :label="item[ElOption_dateType.label]"
                    :value="item[ElOption_dateType.value]"
                    :disabled="item[ElOption_dateType.disabled]"
                  ></ElOption>
                </customElSelect>
                <CetButton
                  class="fl"
                  v-bind="CetButton_prv"
                  v-on="CetButton_prv.event"
                ></CetButton>
                <div class="datePicker mlJ mrJ fl">
                  <el-date-picker
                    v-model="elDate.value"
                    v-bind="elDate"
                    v-on="elDate.event"
                  ></el-date-picker>
                </div>
                <CetButton
                  class="fl"
                  v-bind="CetButton_next"
                  v-on="CetButton_next.event"
                ></CetButton>
              </div>
            </div>
            <div class="content mtJ2">
              <!-- 班次统计数据 -->
              <ShiftStatistics
                :energyStatisticInfo="energyStatisticInfo"
              ></ShiftStatistics>
              <!-- 班组统计数据 -->
              <TeamStatistics
                :energyTypeInfo="energyTypeInfo"
                :top5Data="top5Data"
                :schedulingPlanInfo="schedulingPlanInfo"
                :energyStatisticList="energyStatisticList"
                @selectedDataChanged="selectedDataChanged"
              ></TeamStatistics>
              <!-- 能耗统计数据 -->
              <ShiftChart
                :teamGroupEnergyList="teamGroupEnergyList"
                :cycleType="ElSelect_dateType.value"
              ></ShiftChart>
            </div>
          </div>
        </el-container>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import ShiftStatistics from "./components/shiftStatistics.vue";
import TeamStatistics from "./components/teamStatistics.vue";
import ShiftChart from "./components/shiftChart.vue";
import commonApi from "@/api/custom.js";
import piemCommon from "@/utils/common";
export default {
  name: "teamEnergyConsumption",
  components: { ShiftStatistics, TeamStatistics, ShiftChart },
  data(vm) {
    const setNodeClasses = (treeId, treeNode) => {
      return treeNode.childSelectState == 2
        ? { add: ["halfSelectedNode"] }
        : { remove: ["halfSelectedNode"] };
    };
    return {
      currentNode: null,
      top5Data: [],
      CetTree_1: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "tree_id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: true,
        event: {
          currentNode_out: this.CetTree_1_currentNode_out
        }
      },
      ElSelect_schedulingPlan: {
        value: "",
        style: {},
        event: {}
      },
      ElOptions_schedulingPlan: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      ElOptions_energyType: {
        options_in: [],
        key: "energytype",
        value: "energytype",
        label: "name",
        disabled: "disabled"
      },
      ElSelect_energyType: {
        value: "",
        style: {},
        event: {}
      },
      ElSelect_dateType: {
        value: 14,
        size: "small",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_dateType_change_out
        }
      },
      ElOption_dateType: {
        options_in: [
          {
            id: 14,
            text: $T("月")
          },
          {
            id: 17,
            text: $T("年")
          }
        ],
        key: "id",
        value: "id",
        label: "text",
        disabled: "disabled"
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      },
      elDate: {
        value: vm.$moment().startOf("month").valueOf(),
        "value-format": "timestamp",
        type: "month",
        placeholder: $T("请选择"),
        size: "small",
        clearable: false,
        style: {
          width: "150px"
        },
        pickerOptions: {
          disabledDate(time) {
            const timestamp = time.getTime();
            switch (vm.ElSelect_dateType.value) {
              case 12:
                return timestamp > +vm.$moment(vm.startDayTime).add(1, "d");
              case 14:
                return timestamp > +vm.$moment(vm.startDayTime).add(1, "M");
              case 17:
                return timestamp > +vm.$moment(vm.startDayTime).add(1, "y");
              default:
                return timestamp > Date.now();
            }
          }
        },
        event: {
          change: this.elDate_change
        }
      },
      energyStatisticInfo: {},
      energyStatisticList: [],
      teamGroupEnergyList: [],
      schemeRelatedNode: [],
      requestParameters: {
        teamGroupIdList: [],
        endTime: null,
        startTime: null,
        energyType: "",
        nodeId: "",
        nodeLabel: "",
        schedulingSchemeId: ""
      },
      schedulingPlanInfo: {},
      energyTypeInfo: {},
      visibleTrigger_in: new Date().getTime()
    };
  },
  computed: {
    projectId() {
      let vm = this;
      return vm.$store.state.projectId;
    }
  },
  watch: {
    "elDate.value": {
      handler: function () {
        this.handlerNextBtn();
      },
      deep: true
    },
    "ElSelect_schedulingPlan.value": {
      handler(val) {
        //获取选中的节点数据
        this.getSchedulingPlanData(val);
        this.getSchedulingschemeRelatedNode();
      }
    },
    "ElSelect_energyType.value": {
      handler(val) {
        if (val) {
          this.energyTypeInfo = this.ElOptions_energyType.options_in.find(
            item => item.energytype === val
          );
          this.getSchedulingschemeRelatedNode();
        }
      }
    },
    currentNode: {
      handler: _.debounce(function (val) {
        if (val) {
          this.initData();
        }
      }, 300)
    }
  },
  activated() {
    if (this.$route.path === "/teamEnergyConsumption") {
      this.getSchedulingschemeRelatedNode();
      this.visibleTrigger_in = new Date().getTime();
    }
  },
  methods: {
    disabledNodeColor: piemCommon.disabledNodeColor,
    /**
     * 初始化数据
     */
    initData() {
      this.getClassesEnergyCompare();
      this.getEnergyStatisticList();
      this.getTeamGroupEnergyHistogram();
    },

    /**
     * 根据排班方案 ID 获取对应的排班方案数据
     */
    getSchedulingPlanData(val) {
      if (!val) return (this.schedulingPlanInfo = null);
      const plan = this.ElOptions_schedulingPlan.options_in.find(
        item => item.id === val
      );
      this.schedulingPlanInfo = plan || null;
    },

    /**
     * 选择节点
     */
    CetTree_1_currentNode_out(val) {
      this.currentNode = val;
    },

    /**
     * 查询排班方案关联节点
     */
    getSchedulingschemeRelatedNode: _.debounce(function (val) {
      if (!this.ElSelect_schedulingPlan.value) return;
      let data = {
        schedulingSchemeId: this.ElSelect_schedulingPlan.value
      };
      commonApi.querySchedulingschemeRelatedNode(data).then(res => {
        if (res.code === 0) {
          let relatedNodes = _.get(res, "data", []);
          this.schemeRelatedNode = relatedNodes.map(node => {
            node.tree_id = node.tree_id || `${node.modelLabel}_${node.id}`;
            return node;
          });
          this.getTreeData();
        }
      });
    }, 300),

    /**
     * 获取节点树
     */
    getTreeData() {
      var _this = this;
      var data = {
        energyType: this.ElSelect_energyType.value,
        schedulingSchemeId: this.ElSelect_schedulingPlan.value
      };
      commonApi.getProjectManageTreeEnergytype(data).then(res => {
        if (res.code === 0) {
          piemCommon.setTreeDisabledNode(res.data);
          _this.CetTree_1.inputData_in = res.data;
          const obj = this._.find(
            piemCommon.findFirstChildSelectState(res.data),
            ["childSelectState", 1]
          );
          _this.CetTree_1.selectNode = obj;
        }
      });
    },

    /**
     * 获取能耗类型
     */
    getProjectEnergy() {
      commonApi.queryEnergyConsumeConfig(this.projectId).then(res => {
        if (res.code === 0) {
          this.ElOptions_energyType.options_in = _.get(res, "data", []);
          this.ElSelect_energyType.value =
            this.ElOptions_energyType.options_in[0].energytype;
        }
      });
    },

    /**
     * 获取排班方案
     */
    async getSchedulingPlan() {
      let params = {
        classTeamType: ""
      };
      let res = await commonApi.queryAllSchedulingscheme(params);
      if (res.code === 0) {
        let list = this._.get(res, "data", []);
        this.ElOptions_schedulingPlan.options_in = list.length
          ? list.filter(item => item.classTeamType === 2)
          : [];
        this.ElSelect_schedulingPlan.value =
          this.ElOptions_schedulingPlan.options_in[0]?.id || "";
      }
    },

    /**
     * 处理下一个按钮的状态
     */
    handlerNextBtn() {
      let time, dateStr, maxTime;
      switch (this.ElSelect_dateType.value) {
        case 17:
          time = this.startYearTime;
          dateStr = "year";
          maxTime = this.$moment(time).add(1, "y").startOf(dateStr).valueOf();
          break;
        case 14:
          time = this.startMonthTime;
          dateStr = "month";
          maxTime = this.$moment(time).add(1, "M").startOf(dateStr).valueOf();
          break;
        case 12:
          time = this.startDayTime;
          dateStr = "date";
          maxTime = this.$moment(time).add(1, "d").startOf(dateStr).valueOf();
          break;
        default:
          break;
      }
      if (
        this.$moment(this.elDate.value).startOf(dateStr).valueOf() >= maxTime
      ) {
        this.CetButton_next.disable_in = true;
      } else {
        this.CetButton_next.disable_in = false;
      }
    },

    /**
     * 处理日期类型选择框的改变事件
     */
    ElSelect_dateType_change_out(val) {
      let time = this.elDate.value;
      if (new Date(time) > new Date()) {
        this.elDate.value = this._.cloneDeep(new Date().getTime());
      }
      if (val === 17) {
        this.elDate.type = "year";
      } else if (val === 14) {
        this.elDate.type = "month";
      } else if (val === 12) {
        this.elDate.type = "date";
      }
      this.handlerNextBtn();
      this.initData();
    },

    /**
     * 处理上一个按钮的点击事件
     */
    CetButton_prv_statusTrigger_out(val) {
      if (this.ElSelect_dateType.value === 14) {
        this.elDate.value = this.$moment(this.elDate.value)
          .subtract(1, "month")
          .valueOf();
      } else if (this.ElSelect_dateType.value === 17) {
        this.elDate.value = this.$moment(this.elDate.value)
          .subtract(1, "year")
          .valueOf();
      } else if (this.ElSelect_dateType.value === 12) {
        this.elDate.value = this.$moment(this.elDate.value)
          .subtract(1, "day")
          .valueOf();
      }
      this.initData();
    },

    /**
     * 处理下一个按钮的点击事件
     */
    CetButton_next_statusTrigger_out(val) {
      if (this.ElSelect_dateType.value === 14) {
        this.elDate.value = this.$moment(this.elDate.value)
          .add(1, "month")
          .valueOf();
      } else if (this.ElSelect_dateType.value === 17) {
        this.elDate.value = this.$moment(this.elDate.value)
          .add(1, "year")
          .valueOf();
      } else if (this.ElSelect_dateType.value === 12) {
        this.elDate.value = this.$moment(this.elDate.value)
          .add(1, "day")
          .valueOf();
      }
      this.initData();
    },

    /**
     * 处理日期选择器的改变事件
     */
    elDate_change(val) {
      this.initData();
    },

    /**
     * 构建请求参数
     * @returns {Object} 包含请求所需参数的对象
     */
    buildRequestParams() {
      const { startTime, endTime } = this.calculateTimeRange();
      return {
        endTime,
        startTime,
        energyType: this.ElSelect_energyType.value,
        nodeId: this.currentNode.id,
        nodeLabel: this.currentNode.modelLabel,
        schedulingSchemeId: this.ElSelect_schedulingPlan.value
      };
    },
    selectedDataChanged(selectedData) {
      this.getClassesEnergyCompare(selectedData);
    },
    /**
     * 获取班次对比数据
     */
    /**
     * 获取班次对比数据
     * @param {Object} selectedData - 包含选中的班组和班次信息
     * @param {Array} selectedData.selectedTeamGroup - 选中的班组ID列表
     * @param {Array} selectedData.selectedClass - 选中的班次ID列表
     */
    getClassesEnergyCompare(selectedData) {
      if (!this.currentNode?.id) return;
      // 构建请求参数
      this.requestParameters = this.buildRequestParams();

      // 如果有选中的班组或班次，更新请求参数
      if (selectedData) {
        const { index } = selectedData;
        this.requestParameters.teamGroupIdList = _.get(
          selectedData,
          "selectedTeamGroup",
          []
        );
        this.requestParameters.classesConfigIdList = _.get(
          selectedData,
          "selectedClass",
          []
        );
        // 发起请求
        this.fetchData(commonApi.queryTeamEnergyClassesEnergy, index);
      } else {
        // 发起请求
        this.fetchData(commonApi.queryClassesEnergyCompare);
      }
    },

    /**
     * 提取公共的请求和数据处理逻辑
     * @param {Function} apiMethod - 要调用的API方法
     * @param {string} dataKey - 存储响应数据的属性名
     */
    fetchData(apiMethod, index) {
      apiMethod(this.requestParameters).then(res => {
        if (res.code === 0) {
          let data = _.get(res, "data", []);
          if (Array.isArray(data)) {
            if (Array.isArray(data) && data.length > 4) {
              data = data.slice(0, 4);
            }
            this.energyStatisticList = data;
          } else if (typeof data === "object" && data !== null) {
            this.$set(this.energyStatisticList, index, data);
          }
        }
      });
    },
    /**
     * 班组用能柱状图查询
     */
    getTeamGroupEnergyHistogram() {
      if (!this.currentNode?.id) return;
      this.requestParameters = this.buildRequestParams();
      this.requestParameters.aggregationCycle = this.ElSelect_dateType.value;
      commonApi
        .queryTeamGroupEnergyHistogram(this.requestParameters)
        .then(res => {
          if (res.code === 0) {
            this.teamGroupEnergyList = _.get(res, "data", []);
          }
        });
    },

    /**
     * 查询班组能耗数据
     */
    getEnergyStatisticList() {
      if (!this.currentNode?.id) return;
      this.requestParameters = this.buildRequestParams();
      commonApi.queryTeamGroupEnergy(this.requestParameters).then(res => {
        if (res.code === 0) {
          this.energyStatisticInfo = _.get(res, "data", {});
        }
      });
    },

    /**
     * 提取计算开始和结束时间的逻辑到单独方法
     */
    calculateTimeRange() {
      let startTime, endTime;
      switch (this.ElSelect_dateType.value) {
        case 17: // 按年统计
          startTime = this.$moment(this.elDate.value).startOf("year").valueOf();
          endTime = this.$moment(this.elDate.value)
            .add(1, "year")
            .startOf("year")
            .valueOf();
          break;
        case 14: // 按月统计
          startTime = this.$moment(this.elDate.value)
            .startOf("month")
            .valueOf();
          endTime = this.$moment(this.elDate.value)
            .add(1, "month")
            .startOf("month")
            .valueOf();
          break;
        default:
          startTime = this.elDate.value;
          endTime = this.elDate.value;
      }
      return { startTime, endTime };
    }
  },
  mounted() {
    this.getProjectEnergy();
    this.getSchedulingPlan();
  }
};
</script>
<style lang="scss" scoped>
.page {
  height: 100%;
  position: relative;
  .eem-container {
    height: 100%;
  }
  .header {
    height: 40px;
    line-height: 40px;
  }
}
</style>
