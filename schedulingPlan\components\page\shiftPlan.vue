<template>
  <div class="teamList">
    <div>
      <CetButton
        class="mtJ2 mbJ2"
        v-bind="CetButton_add"
        v-on="CetButton_add.event"
      ></CetButton>
    </div>
    <div class="cardBox" v-if="classesschemeList.length">
      <div
        class="itemCard"
        v-for="(item, index) in classesschemeList"
        :key="index"
      >
        <div class="flex items-center">
          <div class="flex-1 title">
            {{ item.name }}
          </div>
          <div class="flex-1 text-right items-center">
            <omega-icon
              class="editIcon"
              symbolId="edit-hol"
              @click="editHandleTeamPlan(item)"
            ></omega-icon>
            <omega-icon
              class="delIcon"
              symbolId="delete-lin"
              @click="deleteHandleTeamPlan(item)"
            ></omega-icon>
          </div>
        </div>
        <div v-for="res in getSortedClassesConfigList(item)" :key="res.id">
          <div class="flex items-center mtJ2">
            <div class="flex-1 classesBox">
              <span
                class="classesName"
                :style="{ width: nameMaxWidth() + 'px' }"
              >
                {{ res.name }}
              </span>
              <span class="time">
                {{
                  displayTimeRange(
                    res.startTime,
                    res.endTime,
                    res.order,
                    getSortedClassesConfigList(item)
                  )
                }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="emptyData" v-else>
      <img :src="emptyDataImg" />
      <div class="emptyData">列表暂无数据</div>
    </div>
    <AddOrEditShiftPlan
      :schemeInfo="schemeInfo"
      v-bind="addOrEditShiftPlan"
      @save_out="getQueryClassesscheme"
    />
  </div>
</template>

<script>
import commonApi from "@/api/custom.js";
import AddOrEditShiftPlan from "../dialog/AddOrEditShiftPlan.vue";
export default {
  name: "shiftPlan",
  components: { AddOrEditShiftPlan },
  props: {
    schemeInfo: {
      type: Object,
      default: () => {}
    },
    tableList: {
      type: Array,
      default: () => []
    },
    refreshTrigger_in: {
      type: Number
    }
  },
  data() {
    return {
      CetButton_add: {
        class: "fl",
        visible_in: true,
        disable_in: false,
        title: "新增班次方案",
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_add_teamPlan_out
        }
      },
      addOrEditShiftPlan: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null,
        isAdd: null
      },
      classesschemeList: [],
      emptyDataImg: require("../../../assets/empty.png"),
      maxWidth: 50,
      isFirstLoad: true, // 标记是否是首次加载
      shouldRefreshParent: false // 标记是否所有数据都被删除了
    };
  },
  watch: {
    schemeInfo: {
      handler(newVal) {
        if (newVal.id) {
          this.getQueryClassesscheme();
        }
      },
      immediate: true,
      deep: true
    },
    refreshTrigger_in: {
      deep: true,
      handler(newVal) {
        this.CetButton_add.disable_in = this.tableList.length === 0;
      }
    }
  },
  methods: {
    /*
     * 时间日期根据order数据排序
     */
    getSortedClassesConfigList(item) {
      return item.classesConfigList.slice().sort((a, b) => a.order - b.order);
    },

    /*
     * 计算班次名称最大宽度
     */
    nameMaxWidth() {
      let maxLen = 0;
      if (!this.classesschemeList.length) return this.maxWidth;
      this.classesschemeList?.forEach(item => {
        if (item.classesConfigList) {
          item.classesConfigList.forEach(res => {
            const len = res.name.length;
            if (len > maxLen) {
              maxLen = len;
            }
          });
        }
      });
      const charWidth = 14;
      return Math.min(280, Math.max(50, maxLen * charWidth));
    },

    /*
     * 查询班次方案
     */
    async getQueryClassesscheme() {
      let params = {
        schedulingSchemeId: this.schemeInfo.id
      };
      let res = await commonApi.queryClassesscheme(params);
      if (res.code === 0) {
        this.classesschemeList = this._.get(res, "data", []);
        if (this.shouldRefreshParent && !this.isFirstLoad) {
          this.$emit("refreshTable");
          this.shouldRefreshParent = false; // 重置标志位
        }
        if (this.isFirstLoad) {
          this.isFirstLoad = false;
        }
      }
    },

    /*
     * 新增排班方案按钮
     */
    CetButton_add_teamPlan_out() {
      if (!this.tableList?.length) return;
      if (this.classesschemeList.length > 3) {
        this.colorCurrentIndex = 0;
        return this.$message({
          message: $T("最多添加4个班次方案"),
          type: "warning"
        });
      }
      this.addOrEditShiftPlan.inputData_in = null;
      this.addOrEditShiftPlan.isAdd = true;
      this.shouldRefreshParent = true;
      this.addOrEditShiftPlan.visibleTrigger_in = Date.now();
    },

    /*
     * 编辑班次方案按钮
     */
    editHandleTeamPlan(row) {
      this.addOrEditShiftPlan.inputData_in = row;
      this.addOrEditShiftPlan.isAdd = false;
      this.addOrEditShiftPlan.visibleTrigger_in = +new Date();
    },

    /*
     * 删除班次方案按钮
     */
    deleteHandleTeamPlan(row) {
      this.$confirm("确定要删除所选项吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(async () => {
          const res = await commonApi.deleteClassesscheme({ id: row.id });
          if (res.code !== 0) return;
          this.$message({
            message: $T("删除成功"),
            type: "success"
          });
          this.getQueryClassesscheme();
          this.shouldRefreshParent = true;
        })
        .catch(() => {});
    },

    /*
     * 毫秒数转换为时间
     */
    getTimeData(time) {
      if (time || time === 0) {
        const hours = Math.floor(time / (1000 * 60 * 60));
        const minutes = Math.floor((time % (1000 * 60 * 60)) / (1000 * 60));
        return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
          2,
          "0"
        )}`;
      }
      return "";
    },

    /*
     * 时间范围显示
     */
    displayTimeRange(startTime, endTime, order, totalOrders) {
      let formattedStart = this.getTimeData(startTime);
      let formattedEnd = this.getTimeData(endTime);

      // 处理开始时间为负数的情况
      if (startTime < 0) {
        formattedStart = `昨日${this.getTimeData(
          startTime + 24 * 60 * 60 * 1000
        )}`;
      }

      if (endTime > 24 * 60 * 60 * 1000) {
        let formattedTime = this.getTimeData(endTime - 24 * 60 * 60 * 1000);
        formattedEnd = `次日${formattedTime}`;
      }

      // 处理第一个班次跨天的情况
      if (order === 1 && endTime < startTime) {
        formattedStart = `昨日${this.getTimeData(startTime)}`;
      }

      // 处理最后一个班次跨天的情况
      if (order === totalOrders?.length && endTime < startTime) {
        formattedEnd = `次日${this.getTimeData(endTime)}`;
      }
      // 确保开始时间和结束时间不会同时有前缀
      if (formattedStart.includes("昨日") && formattedEnd.includes("次日")) {
        if (order === 1) {
          formattedEnd = this.getTimeData(endTime);
        } else if (order === totalOrders) {
          formattedStart = this.getTimeData(startTime);
        }
      }
      if (!formattedStart && !formattedEnd) {
        return "--";
      } else if (formattedStart && formattedEnd) {
        return `${formattedStart} - ${formattedEnd}`;
      } else {
        return formattedStart || formattedEnd || "--";
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.teamList {
  display: flex;
  flex-direction: column;
  .cardBox {
    .itemCard {
      padding: 24px;
      box-sizing: border-box;
      border: 1px solid;
      @include border-color(B2);
      border-radius: 4px;
      margin-bottom: 16px;
      .title {
        white-space: nowrap;
        font-weight: 500;
      }
      .editIcon,
      .delIcon {
        cursor: pointer;
        margin-left: 10px;
        font-size: 20px;
      }
      .editIcon {
        @include font_color(T3);
      }
      .delIcon {
        @include font_color(Sta3);
      }
      .classesBox {
        .classesName {
          white-space: nowrap;
          display: inline-block;
          font-weight: 500;
          @include font_color(T2);
          margin-right: 10px;
        }
        .time {
          @include font_color(T3);
        }
      }
    }
  }
  .emptyData {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 50%;
    transform: translateY(-34%);
    img {
      width: 200px;
      height: 200px;
    }
    div {
      margin-top: 20px;
      @include font_color(T3);
    }
  }
}
</style>
