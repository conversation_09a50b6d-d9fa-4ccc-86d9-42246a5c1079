<template>
  <CetDialog class="CetDialog" v-bind="CetDialog_1" :before-close="beforeClose">
    <div class="eem-cont-c1" style="padding-bottom: 1px; flex: 1">
      <CetForm
        ref="cetFormRefs"
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
        class="flex-column"
        :key="visibleTrigger_in"
        style="height: calc(100% - 16px)"
      >
        <el-form-item label="班次方案名称" prop="className">
          <ElInput
            v-model.trim="CetForm_1.data.className"
            v-bind="ElInput_input_name"
            maxlength="20"
            v-on="ElInput_input_name.event"
          ></ElInput>
        </el-form-item>
        <div
          v-for="(item, idx) in CetForm_1.data.classesConfigList"
          :key="item.key"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item
                label="班次名称"
                :prop="`classesConfigList.${idx}.name`"
                :rules="CetForm_1.rules.name"
              >
                <el-input
                  v-model.trim="item.name"
                  maxlength="20"
                  placeholder="请输入班次名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="14">
              <div class="flex">
                <el-form-item
                  label="选择时段"
                  :prop="`classesConfigList.${idx}.startTime`"
                  :rules="CetForm_1.rules.startTime"
                >
                  <el-time-select
                    placeholder="开始时间"
                    v-model="item.startTime"
                    @change="handleTimeChange(item)"
                    :picker-options="{
                      start: '00:00',
                      step: '00:30',
                      end: '24:00'
                    }"
                  ></el-time-select>
                </el-form-item>
                <span style="margin-top: 30px" class="mlJ1 mrJ1">至</span>
                <el-form-item
                  label=""
                  :prop="`classesConfigList.${idx}.endTime`"
                  :rules="CetForm_1.rules.endTime"
                  class="endTimeBox"
                >
                  <el-time-select
                    placeholder="结束时间"
                    v-model="item.endTime"
                    @change="handleTimeChange(item)"
                    :picker-options="{
                      start: '00:00',
                      step: '00:30',
                      end: '24:00'
                    }"
                  ></el-time-select>
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="4" class="">
              <div v-if="idx === 0" class="iconBox" @click="addShift(idx)">
                <omega-icon class="plusIcon" symbolId="plus-lin" />
              </div>
              <div v-else class="flex items-center">
                <div class="iconBox mrJ2" @click="addShift(idx)">
                  <omega-icon class="plusIcon" symbolId="plus-lin" />
                </div>
                <div
                  icon="el-icon-delete"
                  class="iconBox"
                  @click="removeShift(idx)"
                >
                  <omega-icon class="delIcon" symbolId="delete-lin" />
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </CetForm>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="mlJ1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
import commonApi from "@/api/custom.js";
export default {
  name: "AddOrEditShiftPlan",
  props: {
    visibleTrigger_in: Number,
    closeTrigger_in: Number,
    inputData_in: Object,
    isAdd: Boolean,
    schemeInfo: Object
  },
  data() {
    return {
      ElInput_input_name: {
        value: "",
        placeholder: "请输入",
        style: {
          width: "100%"
        },
        event: {}
      },
      CetDialog_1: {
        showClose: true,
        width: "640px",
        title: "",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime()
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {
          classesConfigList: [
            {
              key: Date.now(), // 唯一标识
              name: "", // 默认班次名称
              timeRange: null,
              order: 1 // 排序值
            }
          ]
        },
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        rules: {
          className: [
            {
              required: true,
              message: "请输入班次方案名称,且不超过20个字符",
              trigger: "blur"
            }
          ],
          name: [
            {
              required: true,
              message: "请输入班次名称",
              trigger: "blur"
            }
          ],
          startTime: [
            {
              required: true,
              message: "开始时间不能为空",
              trigger: ["blur", "change"]
            },
            {
              validator: this.validateTimeIntersection, // 自定义校验方法
              trigger: ["blur", "change"]
            }
          ],
          endTime: [
            {
              required: true,
              message: "结束时间不能为空",
              trigger: ["blur", "change"]
            },
            {
              validator: this.validateTimeIntersection, // 自定义校验方法
              trigger: ["blur", "change"]
            }
          ]
        },
        event: {
          saveData_out: this.CetForm_1_saveData_out
        }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    async visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      this.CetDialog_1.title = `${this.isAdd ? "新增" : "修改"}班次方案`;
      if (!this.isAdd) {
        const info = this.inputData_in;
        this.CetForm_1.data = { ...info }; // 深拷贝
        this.CetForm_1.data.className = info.name;
        this.CetForm_1.data.classesConfigList = this.transformClassesConfigList(
          info.classesConfigList
        );
      }
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
      this.initData();
    }
  },
  methods: {
    validateTimeIntersection(rule, value, callback) {
      const currentField = rule.field;
      const [indexStr, timeType] = currentField.split(".").slice(-2);
      const currentIndex = parseInt(indexStr.replace(/\D/g, ""), 10);
      const tempClassesConfigList = [...this.CetForm_1.data.classesConfigList];
      const currentItem = tempClassesConfigList[currentIndex];
      if (timeType === "startTime") {
        currentItem.startTime = value;
      } else {
        currentItem.endTime = value;
      }
      const classesConfigVOList = this.generateClassesConfigVOList(
        tempClassesConfigList
      );
      const flag = this.checkTimeIntersection(classesConfigVOList);
      if (flag) {
        return callback(new Error(`时间段存在交集，请调整`));
      }
      callback();
    },
    /**
     * 弹窗确认按钮
     */
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },

    /**
     * 弹窗取消按钮
     */
    CetButton_cancel_statusTrigger_out(val) {
      this.initData();
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
    },

    /**
     * 提交表单
     */
    async CetForm_1_saveData_out() {
      if (!this.schemeInfo.id) return this.$message.error("请选择排班方案");
      let clonedClassesConfigVOList = this.generateClassesConfigVOList(
        this.CetForm_1.data.classesConfigList
      );
      const classesConfigVOList = JSON.parse(
        JSON.stringify(clonedClassesConfigVOList)
      );
      if (this.validateTimeRange(classesConfigVOList))
        return this.$message.warning("时间跨度累计超过24小时，请调整");
      const data = {
        classesConfigVOList,
        schedulingSchemeId: this.schemeInfo.id,
        name: this.CetForm_1.data.className,
        id: this.CetForm_1.data.id || ""
      };
      const res = await commonApi.addOrUpdateClassesscheme(data);
      if (res.code !== 0) return;
      this.$message({
        message: $T("保存成功"),
        type: "success"
      });
      this.$emit("save_out");
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
      this.initData();
    },

    /**
     * 校验当前时间段不能超过24小时
     */
    validateTimeRange(times) {
      // 对数组进行深拷贝，避免影响原数组顺序
      const copiedTimes = JSON.parse(JSON.stringify(times));
      // 对拷贝后的时间进行排序
      copiedTimes.sort((a, b) => a.startTime - b.startTime);
      // 初始化总时长和当前结束时间
      let totalDuration = 0;
      let currentEndTime = 0;
      times.forEach(time => {
        let startTime = time.startTime;
        let endTime = time.endTime;
        // 处理跨天的情况
        if (endTime < startTime) {
          endTime += 24 * 60 * 60 * 1000;
        }
        // 如果当前开始时间小于当前结束时间，说明有重叠，更新开始时间
        if (startTime < currentEndTime) {
          startTime = currentEndTime;
        }
        // 计算当前时间段的时长
        const duration = endTime - startTime;
        // 更新总时长和当前结束时间
        totalDuration += duration;
        currentEndTime = endTime;
      });
      return totalDuration > 24 * 60 * 60 * 1000;
    },

    /**
     * 校验当前数据是存在交集且不允许提交
     */
    checkTimeIntersection(times) {
      // 如果数组长度小于2，直接返回false
      if (times.length < 2) return false;
      // 遍历所有时间区间，检查每两个区间之间是否存在交集
      for (let i = 0; i < times.length; i++) {
        for (let j = i + 1; j < times.length; j++) {
          const time1 = times[i];
          const time2 = times[j];
          // 判断两个时间区间是否存在交集，排除边界重合情况
          const hasIntersection =
            time1.startTime < time2.endTime &&
            time1.endTime > time2.startTime &&
            time1.startTime !== time2.endTime &&
            time1.endTime !== time2.startTime;
          // 检查是否满足毫秒大于86400000时不视为交集的规则
          if (hasIntersection) {
            if (
              Math.abs(time1.endTime - time2.startTime) > 86400000 ||
              Math.abs(time2.endTime - time1.startTime) >= 86400000
            ) {
              return false; // 跳过，不算交集
            }
            return true; // 存在交集
          }
        }
      }

      // 判断跨天情况：最后一条数据的结束时间和第一条数据的开始时间是否冲突
      const last = times[times.length - 1];
      const first = times[0];
      if (
        last.endTime > first.startTime &&
        last.endTime < first.startTime &&
        last.endTime !== first.startTime
      ) {
        return true;
      }
      return false; // 不存在交集
    },

    /**
     * 弹窗关闭前触发的事件
     */
    beforeClose() {
      this.CetDialog_1.closeTrigger_in = new Date().getTime();
      this.initData();
    },
    /**
     * 数据初始化
     */
    initData() {
      this.CetForm_1.data = {
        classesConfigList: [
          {
            key: Date.now(), // 唯一标识
            name: "", // 默认班次名称
            timeRange: null,
            order: 1 // 排序值
          }
        ]
      };
      this.$refs.cetFormRefs.$refs.cetForm.resetFields();
      this.$refs.cetFormRefs.$refs.cetForm.clearValidate();
    },

    /**
     * 新增班次
     */
    addShift(index) {
      // 如果已经有4条数据，不允许新增
      if (this.CetForm_1.data.classesConfigList.length >= 4) return;
      // 获取当前选中行的 order 值
      const currentOrder = this.CetForm_1.data.classesConfigList[index].order;
      // 新增班次项
      const newShift = {
        key: new Date().getTime(), // 唯一标识
        name: "",
        startTime: "",
        endTime: "",
        order: currentOrder + 1 // 新增项的 order 值为当前项的 order + 1
      };

      // 在指定位置插入新班次
      this.CetForm_1.data.classesConfigList.splice(index + 1, 0, newShift);

      // 更新后续数据的 order 值
      this.updateShiftOrder();
    },

    /**
     * 更新排序顺序
     */
    updateShiftOrder() {
      this.CetForm_1.data.classesConfigList.forEach((item, idx) => {
        item.order = idx + 1; // order 值从 1 开始
      });
    },

    /**
     * 删除班次
     */
    removeShift(index) {
      this.CetForm_1.data.classesConfigList.splice(index, 1);
      this.updateShiftOrder();
    },

    /**
     * 单独计算选择的日期-时间转时间戳
     */
    generateClassesConfigVOList(shifts) {
      const sortedShifts = shifts.slice().sort((a, b) => a.order - b.order);
      let isCrossDay = false;
      return sortedShifts
        .map((item, index) => {
          const startTime = this.$moment(item.startTime, "HH:mm");
          const endTime = this.$moment(item.endTime, "HH:mm");
          if (!startTime.isValid() || !endTime.isValid()) {
            return null;
          }
          // 计算时间差和毫秒数
          const diff = endTime.diff(startTime);
          let startTimeMs = startTime.diff(startTime.clone().startOf("day"));
          let endTimeMs = endTime.diff(endTime.clone().startOf("day"));
          if (item.startTime === "24:00") {
            startTimeMs = 24 * 60 * 60 * 1000;
          }
          if (item.endTime === "24:00") {
            endTimeMs = 24 * 60 * 60 * 1000;
          }
          // 处理跨天逻辑
          if (index === 0 && diff < 0) {
            const negativeMs = 24 * 60 * 60 * 1000 - startTimeMs;
            return {
              name: item.name,
              startTime: -negativeMs,
              endTime: endTimeMs,
              order: item.order
            };
          } else if (index > 0 && diff < 0 && !isCrossDay) {
            endTimeMs += 24 * 60 * 60 * 1000;
            isCrossDay = true;
          } else if (index > 0 && isCrossDay) {
            endTimeMs += 24 * 60 * 60 * 1000;
          }
          return {
            name: item.name,
            startTime: startTimeMs,
            endTime: endTimeMs,
            order: item.order
          };
        })
        .filter(Boolean);
    },

    /**
     * 时间处理-时间戳转日期
     */
    transformClassesConfigList(classesConfigList) {
      if (!classesConfigList || !classesConfigList.length)
        return [
          {
            key: Date.now(), // 唯一标识
            name: "", // 默认班次名称
            timeRange: null,
            order: 1 // 排序值
          }
        ];
      const formatTime = timestamp => {
        const isNegative = timestamp < 0;
        const absTimestamp = Math.abs(timestamp);
        const finalTimestamp = isNegative
          ? 24 * 60 * 60 * 1000 - absTimestamp
          : absTimestamp;
        return this.$moment.utc(finalTimestamp).format("HH:mm");
      };
      return classesConfigList.map(item => ({
        key: item.id || Date.now(),
        name: item.name,
        order: item.order,
        startTime: formatTime(item.startTime),
        endTime: formatTime(item.endTime)
      }));
    },

    /**
     * 处理时间范围选择变化
     */
    handleTimeChange(item) {
      const start = this.$moment(item.startTime, "HH:mm");
      const end = this.$moment(item.endTime, "HH:mm");
      if (start.isValid() && end.isValid()) {
        // 判断开始时间和结束时间是否相同
        if (start.isSame(end)) {
          this.$message.warning("开始时间和结束时间不能相同");
          item.endTime = null;
          return;
        }
        const diff = end.diff(start, "hours");
        if (diff > 24) {
          this.$message.warning("开始时间和结束时间不能超过 24 小时");
          // 重置时间选择
          item.startTime = null;
          item.endTime = null;
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  :deep(.el-dialog__body) {
    display: flex;
    height: 100%;
  }
  :deep(.el-form-item) {
    .el-form-item__label {
      line-height: 24px;
      text-align: left;
    }
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
  .el-row {
    display: flex;
    align-items: center;
    .endTimeBox {
      ::v-deep .el-date-editor {
        margin-top: 24px;
      }
    }
    .iconBox {
      cursor: pointer;
      width: 28px;
      height: 28px;
      text-align: center;
      border-radius: 4px;
      padding-top: 2px;
      box-sizing: border-box;
      margin-top: 4px;
      @include background_color(BG2);
      .plusIcon {
        font-size: 24px;
        @include font_color(T4);
      }
      .delIcon {
        font-size: 18px;
        margin-top: 2px;
        @include font_color(T4);
      }
    }
  }
}
</style>
