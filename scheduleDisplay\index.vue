<template>
  <div class="page">
    <div class="fullwidth">
      <div class="padding0 header flex items-center">
        <div>
          <CetButton
            class="fl"
            v-bind="CetButton_prv"
            v-on="CetButton_prv.event"
          ></CetButton>
          <div class="datePicker mlJ mrJ fl">
            <el-date-picker
              v-model="elDate.value"
              v-bind="elDate"
              :disabled="isConfigureShiftPlan"
              v-on="elDate.event"
            ></el-date-picker>
          </div>
          <CetButton
            class="fl"
            v-bind="CetButton_next"
            v-on="CetButton_next.event"
          ></CetButton>
          <customElSelect
            v-model="ElSelect_schedulingPlan.value"
            v-bind="ElSelect_schedulingPlan"
            :disabled="isConfigureShiftPlan"
            v-on="ElSelect_schedulingPlan.event"
            class="fl mlJ1"
            prefix_in="排班方案"
          >
            <ElOption
              v-for="item in ElOption_schedulingPlan.options_in"
              :key="item[ElOption_schedulingPlan.key]"
              :label="item[ElOption_schedulingPlan.label]"
              :value="item[ElOption_schedulingPlan.value]"
              :disabled="item[ElOption_schedulingPlan.disabled]"
            ></ElOption>
          </customElSelect>
        </div>
        <div>
          <el-button
            v-if="isConfigureShiftPlan"
            class="fr"
            type="primary"
            @click="save_edit"
          >
            保存
          </el-button>
          <el-button
            v-else
            class="fr"
            v-permission="'schedulingclasses_updatet'"
            type="primary"
            @click="editShiftPlan"
          >
            编辑
          </el-button>
          <el-button
            v-if="isConfigureShiftPlan"
            class="fr mrJ1"
            type=""
            @click="canceleEdit"
          >
            取消编辑
          </el-button>
          <el-button-group class="fr mrJ1" v-if="isConfigureShiftPlan">
            <el-button type="" @click="loopSetting">
              循环设置班组
              <el-tooltip effect="light" placement="bottom">
                <omega-icon
                  class="tipsIcon icon-size-I0"
                  symbolId="attention"
                />
                <div slot="content" v-html="tooltipContent"></div>
              </el-tooltip>
            </el-button>
          </el-button-group>
          <el-button
            class="fr mrJ1"
            type=""
            @click="batchSettings"
            v-if="isConfigureShiftPlan"
          >
            批量设置班次
          </el-button>
          <el-button
            v-if="isConfigureShiftPlan"
            class="fr mrJ1"
            type=""
            @click="clearSchedulingConfigForDate(null)"
          >
            一键清空
          </el-button>
        </div>
      </div>
    </div>
    <el-main class="padding0 mainBox">
      <CalendarControl v-model="elDate.value">
        <template slot="dateCell" slot-scope="{ date, data }">
          <div class="top calendar-date-cell">
            <span></span>
            <span
              v-if="
                isConfigureShiftPlan &&
                getSchedulingConfigForDate(date)?.schedulingClassesConfigVOS
                  ?.length
              "
              class="flex-1 text-left mlJ1 clearText"
              @click="clearSchedulingConfigForDate(date)"
            >
              清除班次
            </span>
            <span>{{ getDay(date) }}</span>
          </div>
          <div
            v-if="getSchedulingConfigForDate(date)"
            class="content"
            ref="targetTeamPlanContent"
          >
            <div
              class="itemCell"
              :class="getItemCellClass(res)"
              v-for="(res, index) in getSchedulingConfigForDate(date)
                .schedulingClassesConfigVOS"
              :key="`${index}-${res.id}`"
            >
              <TextTooltip
                class="name"
                placement="top"
                :content="getTooltip(res)"
                :text="getTooltip(res)"
              ></TextTooltip>
              <span class="text-left time">
                {{
                  formatTime(
                    res.startTime,
                    res.endTime,
                    res.order,
                    getSchedulingConfigForDate(date).schedulingClassesConfigVOS
                  )
                }}
              </span>
              <img
                class="icon icon-size-I0"
                :src="res.color ? associationTeamsIcon : noAssociationTeamsIcon"
                @click.stop="setConfigureTeamPlan(date, res, $event)"
                alt=""
              />
            </div>
          </div>
          <div
            v-else
            class="content"
            style="line-height: 80px"
            ref="targetShiftPlanContent"
            @click="setConfigureShiftPlan(date, $event)"
          >
            {{
              isConfigureShiftPlan && data.type === "current-month"
                ? "+配置班次方案"
                : ""
            }}
          </div>
        </template>
      </CalendarControl>
      <el-popover
        class="popoverBox"
        ref="popover"
        v-model="isPopoverVisible"
        placement="bottom"
        trigger="manual"
        @hide="isPopoverVisible = false"
      >
        <el-radio-group
          v-model="schemeValue"
          class="flex flex-column"
          @change="handleChange"
        >
          <el-radio
            class="mtJ1"
            v-for="item in classesschemeList"
            :key="item.id"
            :label="item.id"
          >
            {{ item.name }}
          </el-radio>
        </el-radio-group>
      </el-popover>
      <el-popover
        class="popoverBox"
        ref="popoverTeam"
        v-model="isPopoverVisibleTeam"
        placement="right"
        trigger="manual"
        @hide="isPopoverVisibleTeam = false"
      >
        <el-radio-group
          v-model="schemeTeamValue"
          class="flex flex-column"
          @change="handleChangeTeam"
        >
          <el-radio
            class="mtJ1"
            v-for="item in teamsGroupsList"
            :key="item.id"
            :label="item.id"
          >
            {{ item.name }}
          </el-radio>
        </el-radio-group>
      </el-popover>
    </el-main>
    <CycleSettingTeamDialog
      v-bind="cycleSettingTeamInfo"
      :selectedDate="elDate.value"
      :schedulingSchemeId="ElSelect_schedulingPlan.value"
      :schedulingTable="schedulingTable"
      @confirmForm="addTeamConfirmForm"
    ></CycleSettingTeamDialog>
    <BatchSetShiftsDialog
      v-bind="batchSetShiftsInfo"
      :selectedDate="elDate.value"
      :schedulingSchemeId="ElSelect_schedulingPlan.value"
      @confirmForm="addShiftsConfirmForm"
    ></BatchSetShiftsDialog>
  </div>
</template>

<script>
import TextTooltip from "../components/textTooltip.vue";
import BatchSetShiftsDialog from "./components/batchSetShiftsDialog.vue";
import CycleSettingTeamDialog from "./components/cycleSettingTeamDialog.vue";
import commonApi from "@/api/custom.js";
import CalendarControl from "./calendarControl/main.vue";
export default {
  name: "scheduleDisplay",
  components: {
    CalendarControl,
    CycleSettingTeamDialog,
    BatchSetShiftsDialog,
    TextTooltip
  },
  data(vm) {
    return {
      ElSelect_schedulingPlan: {
        value: "",
        style: {
          width: "272px"
        }
      },
      ElOption_schedulingPlan: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      tooltipContent: `
        循环设置说明：
        <br />
        按照选定的循环班组，针对时间段内
        <br />
        所选区间的开始和结束班次，进行自
        <br />
        动排班。使用前请确保已经设置了所
        <br />
        需日期的班次方案以及至少配置了一
        <br />
        天的班组
      `,
      isConfigureShiftPlan: false,
      schedulingTable: [],
      classesschemeList: [],
      teamsGroupsList: [],
      schemeValue: null,
      schemeTeamValue: null,
      isPopoverVisibleTeam: false,
      isPopoverVisible: false,
      selectClassesTimeInfo: {},
      cycleSettingTeamInfo: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      batchSetShiftsInfo: {
        visibleTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        inputData_in: null
      },
      associationTeamsIcon: require("../assets/check.png"),
      noAssociationTeamsIcon: require("../assets/noCheck.png"),
      elDate: {
        value: vm.$moment().startOf("month").valueOf(),
        "value-format": "timestamp",
        type: "month",
        placeholder: $T("请选择"),
        size: "small",
        clearable: false,
        style: {
          width: "150px"
        },
        pickerOptions: {
          // 添加回到今天的快捷键
          shortcuts: [
            {
              text: this.$T("当月"),
              onClick(picker) {
                picker.$emit("pick", new Date());
              }
            }
          ]
        },
        event: {
          change: this.currentDateChange
        }
      },
      // 向前查询按钮组件
      CetButton_prv: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-left",
        event: {
          statusTrigger_out: this.CetButton_prv_statusTrigger_out
        }
      },
      // 向后查询按钮组件
      CetButton_next: {
        visible_in: true,
        disable_in: false,
        title: "",
        size: "small",
        icon: "el-icon-arrow-right",
        event: {
          statusTrigger_out: this.CetButton_next_statusTrigger_out
        }
      }
    };
  },
  watch: {
    "ElSelect_schedulingPlan.value": {
      handler(newVal) {
        if (newVal) {
          this.getSchedulingTable();
          this.getQueryClassesscheme();
          this.getQueryTeamGroupInfo();
        }
      }
    }
  },
  computed: {
    userInfo() {
      var vm = this;
      return vm.$store.state.userInfo;
    }
  },
  methods: {
    /**
     * 根据权限控制颜色
     */
    getItemCellClass(res) {
      const personIdArray = res?.personId ? JSON.parse(res.personId) : [];
      const isUserIncluded = personIdArray.includes(
        parseInt(this.userInfo.userId)
      );
      return res.color || isUserIncluded ? res.color : "noRelation";
    },

    /**
     * 根据权限获取名称
     */
    getTooltip(res) {
      const personIdArray = res?.personId ? JSON.parse(res.personId) : [];
      if (
        !this.$checkPermission("schedulingclasses_updatet") &&
        personIdArray.length &&
        !personIdArray.includes(parseInt(this.userInfo.userId))
      ) {
        return `休息`;
      }
      return `${res.color ? res.teamGroupName : res.classesConfigName}`;
    },

    /*
     * 根据日期获取对应的排班配置
     */
    getSchedulingConfigForDate(date) {
      const logTime = new Date(new Date(date).setHours(0, 0, 0, 0)).getTime();
      return this.schedulingTable.find(item => item.logTime === logTime);
    },

    /*
     * 切换时间
     */
    currentDateChange(value) {
      this.elDate.value = value;
      this.getSchedulingTable();
    },

    /*
     * 毫秒数转换为时间
     */
    getTimeData(time) {
      if (time || time === 0) {
        const hours = Math.floor(time / (1000 * 60 * 60));
        const minutes = Math.floor((time % (1000 * 60 * 60)) / (1000 * 60));
        return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
          2,
          "0"
        )}`;
      }
      return "";
    },

    /*
     * 时间格式化
     */
    formatTime(startTime, endTime, order, totalOrders) {
      // 处理开始时间为负数的情况
      if (startTime < 0) {
        startTime += 24 * 60 * 60 * 1000;
      }
      const start = this.getTimeData(startTime);
      const end = this.getTimeData(endTime);
      let formattedStart = start;
      let formattedEnd = end;

      if (endTime < startTime && order === 1) {
        formattedStart = `昨日${start}`;
      } else if (startTime < 0) {
        formattedStart = `昨日${start}`;
      }
      if (endTime < startTime && order === totalOrders) {
        formattedEnd = `次日${end}`;
      } else if (endTime > 24 * 60 * 60 * 1000) {
        const adjustedEndTime = endTime - 24 * 60 * 60 * 1000;
        const adjustedEnd = this.getTimeData(adjustedEndTime);
        formattedEnd = `次日${adjustedEnd}`;
      }
      if (!formattedStart && !formattedEnd) {
        return "--";
      } else if (formattedStart && formattedEnd) {
        return `${formattedStart} - ${formattedEnd}`;
      } else {
        return formattedStart || formattedEnd || "--";
      }
    },
    /*
     * 开始编辑
     */
    editShiftPlan() {
      this.isConfigureShiftPlan = true;
      this.CetButton_next.disable_in = true;
      this.CetButton_prv.disable_in = true;
      this.addClassConfirmForm();
    },

    /*
     * 取消编辑
     */
    canceleEdit() {
      this.$confirm("取消编辑前请确认相关配置已经保存", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.schedulingTable = [];
          this.isConfigureShiftPlan = false;
          this.CetButton_next.disable_in = false;
          this.CetButton_prv.disable_in = false;
          this.getSchedulingTable();
        })
        .catch(() => {});
    },

    /**
     * 获取排班表
     */
    async getSchedulingTable() {
      const currentMonthStart = this.$moment(this.elDate.value)
        .startOf("month")
        .valueOf();
      const currentMonthEnd = this.$moment(this.elDate.value)
        .add(1, "month")
        .startOf("month")
        .valueOf();
      const params = {
        startTime: currentMonthStart,
        endTime: currentMonthEnd,
        schedulingSchemeId: this.ElSelect_schedulingPlan.value
      };
      const res = await commonApi.querySchedulingclasses(params);
      this.schedulingTable = [];
      if (res.code === 0) {
        this.schedulingTable = this._.get(res, "data", []);
      }
    },

    /*
     * 获取排班方案
     */
    async getSchedulingPlan() {
      let params = {
        classTeamType: ""
      };
      let res = await commonApi.queryAllSchedulingscheme(params);
      if (res.code === 0) {
        this.ElOption_schedulingPlan.options_in = this._.get(res, "data", []);
        this.ElSelect_schedulingPlan.value =
          this.ElOption_schedulingPlan.options_in[0]?.id || "";
      }
    },

    /*
     * 查询班组方案
     */
    async getQueryTeamGroupInfo() {
      let params = {
        schedulingSchemeId: this.ElSelect_schedulingPlan.value
      };
      let res = await commonApi.queryTeamgroupinfo(params);
      if (res.code === 0) {
        this.teamsGroupsList = this._.get(res, "data", []);
        this.teamsGroupsList.unshift({
          id: "",
          name: "暂不选择"
        });
      }
    },

    /*
     * 查询班次方案
     */
    async getQueryClassesscheme() {
      let params = {
        schedulingSchemeId: this.ElSelect_schedulingPlan.value
      };
      let res = await commonApi.queryClassesscheme(params);
      if (res.code === 0) {
        this.classesschemeList = this._.get(res, "data", []);
        this.addClassConfirmForm();
      }
    },

    /*
     * 循环设置默认班次
     */
    addClassConfirmForm() {
      // 如果只有一条班次方案，则为整个月设置
      if (
        this.classesschemeList?.length === 1 &&
        this.isConfigureShiftPlan &&
        this.schedulingTable.length < 1
      ) {
        const startTime = this.$moment(this.elDate.value)
          .startOf("month")
          .valueOf();
        const endTime = this.$moment(this.elDate.value)
          .endOf("month")
          .valueOf();

        // 遍历整个月的每一天
        for (
          let currentTimestamp = startTime;
          currentTimestamp <= endTime;
          currentTimestamp += 24 * 60 * 60 * 1000
        ) {
          let schedulingEntry = this.schedulingTable.find(
            item => item.logTime === currentTimestamp
          );
          if (!schedulingEntry) {
            schedulingEntry = {
              logTime: currentTimestamp,
              schedulingClassesConfigVOS: []
            };
            this.schedulingTable.push(schedulingEntry);
          }
          const classesConfigList =
            this.classesschemeList[0]?.classesConfigList;
          if (!classesConfigList.length) return;
          // 添加班次信息
          const newClassesConfig = classesConfigList.map(res => {
            return {
              classesConfigId: res.id,
              classesConfigName: res.name,
              startTime: res.startTime,
              endTime: res.endTime,
              ...res
            };
          });

          schedulingEntry.schedulingClassesConfigVOS = [
            ...new Set([
              ...schedulingEntry.schedulingClassesConfigVOS,
              ...newClassesConfig
            ])
          ];
        }
      }
    },

    /*
     * 循环设置班组
     */
    loopSetting() {
      this.cycleSettingTeamInfo.visibleTrigger_in = new Date().getTime();
    },

    /*
     * 批量设置班次
     */
    addShiftsConfirmForm(form) {
      const { periodOfTime, shiftPlan } = form;
      const startTimestamp = this.$moment(periodOfTime[0])
        .startOf("day")
        .valueOf();
      // 计算时间范围内的天数
      const daysDiff = this.$moment(periodOfTime[1])
        .startOf("day")
        .diff(this.$moment(periodOfTime[0]).startOf("day"), "days");

      // 遍历时间范围内的每一天
      for (let i = 0; i <= daysDiff; i++) {
        const currentTimestamp = startTimestamp + i * 24 * 60 * 60 * 1000;
        // 查找当前日期在排班表中的记录
        let schedulingEntry = this.schedulingTable.find(
          item => item.logTime === currentTimestamp
        );
        // 如果当前日期没有记录，则创建一个新记录
        if (!schedulingEntry) {
          schedulingEntry = {
            logTime: currentTimestamp,
            schedulingClassesConfigVOS: []
          };
          this.schedulingTable.push(schedulingEntry);
        }
        schedulingEntry.schedulingClassesConfigVOS =
          shiftPlan.classesConfigList.map(res => {
            return {
              classesConfigId: res.id,
              classesConfigName: res.name,
              startTime: res.startTime,
              endTime: res.endTime,
              ...res
            };
          });
      }
    },

    /*
     * 批量设置班次
     */
    batchSettings() {
      this.batchSetShiftsInfo.visibleTrigger_in = new Date().getTime();
    },

    /*
     * 获取当前日
     */
    getDay(date) {
      return new Date(date).getDate();
    },

    /*
     * 清除当前班次数据
     */
    clearSchedulingConfigForDate(date) {
      if (!date) {
        this.$confirm("此操作将清空所有数据, 是否继续?", "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.schedulingTable = [];
          })
          .catch(() => {});
      } else {
        const logTime = new Date(new Date(date).setHours(0, 0, 0, 0)).getTime();
        this.schedulingTable = this.schedulingTable.filter(
          item => item.logTime !== logTime
        );
      }
    },

    /*
     * 设置班次方案
     */
    handleChange(e) {
      let selectedScheme = this.classesschemeList.find(item => item.id === e);
      if (selectedScheme) {
        const logTime = this.selectClassesTimeInfo.logTime;
        let schedulingEntry = this.schedulingTable.find(
          item => item.logTime === logTime
        );
        if (!schedulingEntry) {
          schedulingEntry = {
            logTime: logTime,
            schedulingClassesConfigVOS: []
          };
          this.schedulingTable.push(schedulingEntry);
        }
        // 避免重复添加相同的班次信息
        const newClassesConfig = selectedScheme.classesConfigList.map(res => {
          return {
            classesConfigId: res.id,
            classesConfigName: res.name,
            startTime: res.startTime,
            endTime: res.endTime,
            ...res
          };
        });
        schedulingEntry.schedulingClassesConfigVOS =
          schedulingEntry.schedulingClassesConfigVOS
            .filter(
              existing =>
                !newClassesConfig.some(
                  newConfig =>
                    newConfig.classesConfigId === existing.classesConfigId
                )
            )
            .concat(newClassesConfig);
      }
      this.schemeValue = null;
      this.isPopoverVisible = false;
    },

    /*
     * 设置班组方案
     */
    handleChangeTeam(e) {
      this.schemeTeamValue = e;
      // 查找匹配的班组方案
      const selectedTeamPlan = this.teamsGroupsList.find(item => item.id === e);
      if (selectedTeamPlan) {
        // 处理单个班组方案
        const newTeamConfig = {
          logTime: this.selectClassesTimeInfo.logTime,
          teamGroupId: selectedTeamPlan.id,
          teamGroupName: selectedTeamPlan?.id ? selectedTeamPlan.name : "",
          ...selectedTeamPlan
        };
        const logTime = this.selectClassesTimeInfo.logTime;
        let schedulingEntry = this.schedulingTable.find(
          item => item.logTime === logTime
        );
        if (schedulingEntry) {
          // 对 schedulingClassesConfigVOS 进行深拷贝
          schedulingEntry.schedulingClassesConfigVOS = JSON.parse(
            JSON.stringify(schedulingEntry.schedulingClassesConfigVOS)
          ).map(r => {
            // 如果当前元素的 id 等于选中的时间信息的 id
            if (
              r.id === this.selectClassesTimeInfo.id &&
              r.startTime === this.selectClassesTimeInfo.startTime &&
              r.endTime === this.selectClassesTimeInfo.endTime
            ) {
              // 返回新的班组配置对象
              return {
                classesConfigId: r.classesConfigId,
                classesConfigName: r.classesConfigName,
                startTime: r.startTime,
                endTime: r.endTime,
                ...newTeamConfig
              };
            }
            // 否则返回原始元素
            return r;
          });
        }
      }
      this.isPopoverVisibleTeam = false;
      this.schemeTeamValue = null;
    },

    /*
     * 提取公共方法来设置弹出框位置
     */
    setPopoverPosition(popoverRef, target, contentElement) {
      const popover = popoverRef.$refs.popper;
      const rect = target.getBoundingClientRect();
      const popoverWidth = popover.offsetWidth;
      const popoverHeight = popover.offsetHeight;
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      const contentRect = contentElement.getBoundingClientRect();
      const itemPopoverWidth = contentRect.width;
      const itemPopoverHeight = contentRect.height;
      // 减去左侧菜单栏宽度
      const menuWidth = 258;
      const adjustedWindowWidth = windowWidth - menuWidth;
      // 初始默认在点击元素的左上方显示
      let top = rect.top - popoverHeight - itemPopoverHeight;
      let left = rect.left - popoverWidth - itemPopoverWidth;
      // 检查上方空间是否足够
      if (top < 0) {
        // 上方空间不足，在下方显示
        top = rect.bottom - 50;
      }

      // 检查左侧空间是否足够
      if (left < 0) {
        // 左侧空间不足，在右侧显示
        left = rect.right - menuWidth;
      }

      // 检查右侧空间是否足够
      if (left + popoverWidth > adjustedWindowWidth) {
        // 右侧空间不足，从右侧边缘开始定位
        left = adjustedWindowWidth - popoverWidth;
      }

      // 检查下方空间是否足够
      if (top + popoverHeight > windowHeight) {
        // 下方空间不足，在上方显示
        top = rect.top - popoverHeight;
      }

      // 动态设置弹出框的位置
      popover.style.position = "absolute";
      popover.style.top = `${top}px`;
      popover.style.left = `${left}px`;
    },

    /*
     * 选择班次
     */
    setConfigureShiftPlan(date, event) {
      if (!this.isConfigureShiftPlan) return;
      this.isPopoverVisible = true; // 显示弹出框
      this.selectClassesTimeInfo.logTime = new Date(
        new Date(date).setHours(0, 0, 0, 0)
      ).getTime();
      this.$nextTick(() => {
        const contentElement = this.$refs.targetShiftPlanContent;
        this.setPopoverPosition(
          this.$refs.popover,
          event.target,
          contentElement
        );
      });
    },

    /*
     * 选择班组
     */
    setConfigureTeamPlan(date, res, event) {
      if (!this.isConfigureShiftPlan) return;
      this.isPopoverVisibleTeam = true; // 显示弹出框
      this.selectClassesTimeInfo = res;
      this.selectClassesTimeInfo.logTime = new Date(
        new Date(date).setHours(0, 0, 0, 0)
      ).getTime();
      this.$nextTick(() => {
        const contentElement = this.$refs.targetTeamPlanContent;
        this.setPopoverPosition(
          this.$refs.popoverTeam,
          event.target,
          contentElement
        );
      });
    },

    /*
     * 点击空白处关闭弹出框
     */
    handleClickOutside(event) {
      const popoverRef = this.$refs.popover.$refs.popper;
      const popoverTeam = this.$refs.popoverTeam.$refs.popper;
      if (!popoverRef || !popoverRef.contains(event.target)) {
        this.isPopoverVisible = false;
      }
      if (!popoverTeam || !popoverTeam.contains(event.target)) {
        this.isPopoverVisibleTeam = false;
      }
    },

    /*
     * 保存or编辑
     */
    async save_edit() {
      const startTime = this.$moment(this.elDate.value)
        .startOf("month")
        .valueOf();
      // 修改为下个月第一天 0 点
      const endTime = this.$moment(this.elDate.value)
        .add(1, "month")
        .startOf("month")
        .valueOf();
      const schedulingClassesConfigDTO = this.schedulingTable.flatMap(item =>
        item.schedulingClassesConfigVOS.map(res => ({
          logTime: item.logTime,
          classesConfigId: res.classesConfigId,
          teamGroupId: res.teamGroupId || null
        }))
      );
      let params = {
        startTime,
        endTime,
        schedulingSchemeId: this.ElSelect_schedulingPlan.value,
        schedulingClassesConfigDTO
      };
      let res = await commonApi.saveSchedulingclasses(params);
      if (res.code === 0) {
        this.$message.success("保存成功");
        this.isConfigureShiftPlan = false;
        this.schedulingTable = [];
        this.getSchedulingTable();
        this.getQueryClassesscheme();
        this.getQueryTeamGroupInfo();
      }
      this.CetButton_next.disable_in = false;
      this.CetButton_prv.disable_in = false;
    },

    /*
     * 循环设置班组
     */
    addTeamConfirmForm(form) {
      const {
        teamStartTime,
        teamTargetTime,
        startShift,
        endShift,
        teamGroupList = []
      } = form;
      // 提取时间戳计算逻辑
      const getStartOfDayTimestamp = time =>
        this.$moment(time).startOf("day").valueOf();
      const startTimestamp = getStartOfDayTimestamp(teamStartTime[0]);
      const endTimestamp = getStartOfDayTimestamp(teamStartTime[1]);
      const startTargetTimestamp = getStartOfDayTimestamp(teamTargetTime[0]);
      const endTargetTimestamp = getStartOfDayTimestamp(teamTargetTime[1]);
      const daysDiff = this.$moment(endTargetTimestamp).diff(
        this.$moment(startTargetTimestamp),
        "days"
      );

      // 1. 获取数据源(子组件有传数据就用子组件传过来的数据源，没有就用当前数据源)
      const sourceList =
        teamGroupList.length > 0 ? teamGroupList : this.schedulingTable;

      // 获取 teamStartTime 时间范围内的数据
      const list = sourceList
        .filter(item => {
          return (
            item.logTime >= startTimestamp &&
            item.logTime <= endTimestamp &&
            item.schedulingClassesConfigVOS.length > 0
          );
        })
        .sort((a, b) => a.logTime - b.logTime);
      let targetList = this.schedulingTable.filter(item => {
        return (
          item.logTime >= startTargetTimestamp &&
          item.logTime <= endTargetTimestamp &&
          item.schedulingClassesConfigVOS.length > 0
        );
      });
      // 使用Map优化数据查找
      const configMap = new Map();
      // 优化startData和endData的处理,过滤开始数据,覆盖数据从什么班组开始
      const startData = this.processStartData(list, startShift, configMap);
      const endData = this.processEndData(list, endShift, configMap);

      // 使用数组预分配优化
      for (let i = 0; i <= daysDiff; i++) {
        const newTimestamp = startTargetTimestamp + i * 24 * 60 * 60 * 1000;
        const originalIndex = i % list.length;
        if (!list[originalIndex]) {
          break;
        }
        // 使用对象扩展优化
        const newData = {
          ...list[originalIndex],
          logTime: newTimestamp,
          schedulingClassesConfigVOS: this.getSchedulingConfigVOS(
            i,
            daysDiff,
            startData,
            endData,
            list[originalIndex]
          )
        };
        if (targetList[i]?.logTime) {
          targetList[i] = newData;
        }
      }

      // 使用Set优化去重
      const timestampSet = new Set(targetList.map(item => item.logTime));
      this.schedulingTable = [
        ...this.schedulingTable.filter(item => !timestampSet.has(item.logTime)),
        ...targetList
      ];
    },

    /*
     * 设置数据覆盖
     */
    getSchedulingConfigVOS(i, daysDiff, startData, endData, originalConfig) {
      if (i === 0 && startData) {
        return startData;
      }
      if (i === daysDiff && endData) {
        return endData;
      }
      return [...originalConfig.schedulingClassesConfigVOS];
    },

    /*
     * 设置数据覆盖开始班组
     */
    processStartData(list, startShift) {
      if (!list.length) return null;
      let startData = null;
      const firstDayConfigs = list[0].schedulingClassesConfigVOS;
      const startIndex = firstDayConfigs.findIndex(
        config => config.classesConfigId === startShift
      );
      if (startIndex !== -1) {
        // 获取 startShift 对应的 order 值
        const startOrder = firstDayConfigs[startIndex].order;
        // 将 order 小于 startOrder 的数据的 color 设为 ""
        startData = firstDayConfigs.map(config => {
          if (config.order < startOrder) {
            return {
              ...config,
              color: "",
              teamGroupId: null,
              teamGroupName: null
            };
          }
          return config;
        });
      }
      return startData;
    },

    /*
     * 设置数据覆盖结束班组
     */
    processEndData(list, endShift) {
      if (!list.length) return null;
      let endData = null;
      const lastDayConfigs = list[list.length - 1].schedulingClassesConfigVOS;
      const endIndex = lastDayConfigs.findIndex(
        config => config.classesConfigId === endShift
      );
      if (endIndex !== -1) {
        // 获取 endShift 对应的 order 值
        const endOrder = lastDayConfigs[endIndex].order;
        // 将 order 大于 endOrder 的数据的 color 设为 ""
        endData = lastDayConfigs.map(config => {
          if (config.order > endOrder) {
            return {
              ...config,
              color: "",
              teamGroupId: null,
              teamGroupName: null
            };
          }
          return config;
        });
      }
      return endData;
    },

    /*
     * 向前查询按钮组件
     */
    CetButton_prv_statusTrigger_out(val) {
      this.elDate.value = this.$moment(this.elDate.value)
        .subtract(1, "month")
        .valueOf();

      this.getSchedulingTable();
    },

    /*
     * 向后查询按钮组件
     */
    CetButton_next_statusTrigger_out(val) {
      this.elDate.value = this.$moment(this.elDate.value)
        .add(1, "month")
        .valueOf();
      this.getSchedulingTable();
    }
  },
  mounted() {
    this.getSchedulingPlan();
    document.addEventListener("click", this.handleClickOutside, true);
  },
  beforeDestroy() {
    document.removeEventListener("click", this.handleClickOutside, true); // 移除全局点击事件监听器
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 24px;
  box-sizing: border-box;
  @include background-color(BG1);
  .header {
    width: 100%;
    height: 32px;
    line-height: 32px;
    justify-content: space-between;
  }
  .mainBox {
    height: calc(100% - 32px);
    .top {
      height: 20px;
    }
    .content {
      height: calc(100% - 20px);
      text-align: center;
      width: 100%;
      font-size: 14px;
      overflow: hidden;
      @include font_color(T4);
      .itemCell {
        height: 20px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;
        align-items: center;
        margin-top: 5px;
        padding: 0 8px;
        box-sizing: border-box;
        .name {
          width: 50px;
          text-align: left;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 12px;
          @include font_color(T3);
        }
        .time {
          width: 116px;
          height: 20px;
          @include font_color(T2);
        }
        .icon {
          cursor: pointer;
        }
      }
      .noRelation {
        @include background_color(BG2);
      }
    }
    .calendar-date-cell {
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
      .clearText {
        @include font_color(Sta3);
        font-size: 12px;
      }
    }
  }

  .tipsIcon {
    @include font_color(B1);
    vertical-align: -2px;
  }
  ::v-deep .el-popover {
    max-height: 300px;
    overflow-y: auto;
  }
}
@import "../assets/bgc.scss";
</style>
