<template>
  <CetChart
    :key="timestamp"
    ref="chart"
    style="height: 237px"
    class="mtJ2"
    :inputData_in="CetChart_trend.inputData_in"
    v-bind="CetChart_trend.config"
  />
</template>

<script>
import omegaTheme from "@omega/theme";
export default {
  name: "shiftChart",
  props: {
    teamGroupEnergyList: {
      type: Array,
      default: () => []
    },
    cycleType: {
      type: Number
    }
  },
  components: {},
  data() {
    return {
      timestamp: Date.now(),
      CetChart_trend: {
        inputData_in: {},
        config: {
          options: {
            legend: {},
            grid: {
              left: 42,
              right: 10,
              top: 35
            },
            xAxis: {},
            yAxis: {
              type: "value"
            },
            dataZoom: [
              {
                type: "inside",
                start: 0,
                end: 100
              },
              {
                start: 0,
                end: 100,
                handleIcon:
                  "M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",
                handleSize: "80%",
                handleStyle: {
                  color: "#fff",
                  shadowBlur: 3,
                  shadowColor: "rgba(0, 0, 0, 0.6)",
                  shadowOffsetX: 2,
                  shadowOffsetY: 2
                },
                left: 150,
                right: 150,
                height: 20,
                bottom: 10
              }
            ],
            series: []
          }
        }
      }
    };
  },
  watch: {
    teamGroupEnergyList: {
      handler(newVal) {
        this.timestamp = Date.now();
        this.CetChart_trend.inputData_in = {};
        this.CetChart_trend.config.options.series = [];
        this.CetChart_trend.config.options.legend = {};
        if (newVal?.length > 0) {
          this.CetChart_trend.config.options.tooltip = {
            trigger: "axis",
            formatter: params => {
              let dataStr = "";
              const filteredData = params.filter(item => {
                return item.name && item.axisValue === item.name;
              });
              if (filteredData?.length) {
                dataStr = filteredData[0].axisValue + "<br/>";
                filteredData.forEach(item => {
                  dataStr += `<div class='flex justify-between'><span>${
                    item.marker
                  }${item.seriesName}：</span><span>${item.value}${
                    newVal[0].energyUnit || ""
                  }</span></div>`;
                });
              }
              return dataStr;
            }
          };
          this.CetChart_trend.config.options.yAxis.name = `单位: (${newVal[0].energyUnit})`;
          const xAxisData = [];
          newVal.forEach(item => {
            let logTime;
            if (this.cycleType === 17) {
              logTime = this.$moment(item.logTime).format("MM");
            } else {
              logTime = this.$moment(item.logTime).format("DD");
            }
            xAxisData.push(logTime);
          });
          this.CetChart_trend.config.options.xAxis = xAxisData.map(
            (item, index) => {
              const data = Array(xAxisData.length).fill("");
              data[index] = item;
              return {
                type: "category",
                position: "bottom",
                data: data
              };
            }
          );
          this.CetChart_trend.config.options.series = this.createSeries(newVal);
        }
      }
    }
  },
  methods: {
    generateList(data) {
      const bars = [];
      const maxTeamGroups = Math.max(
        ...data.map(entry => entry.teamGroupEnergyList.length)
      );
      for (let i = 0; i < maxTeamGroups; i++) {
        bars[i] = Array(data.length).fill(null);
      }
      data.forEach((entry, entryIndex) => {
        entry.teamGroupEnergyList.forEach((team, teamIndex) => {
          bars[teamIndex][entryIndex] = team ? team : null;
        });
      });
      return bars;
    },
    handelDealBar(barDataList) {
      const bars = barDataList.reduce((bars, barData, index) => {
        if (!barData) {
          return bars;
        }
        const { color, energy, teamGroupName: name } = barData;
        const data = new Array(index).fill(null);
        data.push(energy ?? null);
        bars.push({
          itemStyle: {
            color: color ? this.getColor(omegaTheme.theme, color) : ""
          },
          name,
          type: "bar",
          barWidth: 7,
          xAxisIndex: index,
          data,
          ...barData
        });
        return bars;
      }, []);
      return bars;
    },
    createSeries(data) {
      const bars = this.generateList(data);
      const series = [];
      bars.forEach((barData, index) => {
        const bar = this.handelDealBar(barData);
        series.push(...bar);
      });
      return series;
    },
    getColor(theme, key) {
      const lightThemeColors = {
        oil1: "#29b061",
        oil2: "#00a2ff",
        oil3: "#ffc24c",
        oil4: "#e77fbc",
        oil5: "#00c1b2",
        oil6: "#8e43e7",
        oil7: "#ff7f43",
        oil8: "#0056ff",
        oil9: "#d951a0",
        oil10: "#76daff"
      };
      const darkThemeColors = {
        oil1: "#0d86ff",
        oil2: "#00ffff",
        oil3: "#ff782b",
        oil4: "#ff5cbc",
        oil5: "#0dff86",
        oil6: "#a839ff",
        oil7: "#ffe500",
        oil8: "#172fff",
        oil9: "#ff0094",
        oil10: "#76daff"
      };
      if (theme === "light") {
        return lightThemeColors[key] || null;
      } else if (theme === "dark") {
        return darkThemeColors[key] || null;
      }
    }
  }
};
</script>

<style lang="scss" scoped></style>
