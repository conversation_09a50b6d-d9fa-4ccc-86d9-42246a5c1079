<template>
  <CetDialog class="CetDialog" v-bind="CetDialog_1" :before-close="beforeClose">
    <div class="eem-cont-c1" style="padding-bottom: 1px; flex: 1">
      <CetForm
        ref="CetForm"
        :data.sync="CetForm_1.data"
        v-bind="CetForm_1"
        v-on="CetForm_1.event"
        class="flex-column"
        :key="visibleTrigger_in"
        style="height: calc(100% - 16px)"
      >
        <el-form-item label="选择时段" prop="periodOfTime">
          <el-date-picker
            v-model="CetForm_1.data.periodOfTime"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-value="defaultValue"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="班次方案" prop="shiftPlanId">
          <ElSelect
            style="width: 100%"
            v-model="CetForm_1.data.shiftPlanId"
            v-bind="CetForm_1.data"
            v-on="CetForm_1.data.event"
          >
            <ElOption
              v-for="item in ElOption_shiftPlan.options_in"
              :key="item[ElOption_shiftPlan.key]"
              :label="item[ElOption_shiftPlan.label]"
              :value="item[ElOption_shiftPlan.value]"
              :disabled="item[ElOption_shiftPlan.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
      </CetForm>
    </div>
    <span slot="footer">
      <CetButton
        v-bind="CetButton_cancel"
        v-on="CetButton_cancel.event"
      ></CetButton>
      <CetButton
        class="mlJ1"
        v-bind="CetButton_confirm"
        v-on="CetButton_confirm.event"
      ></CetButton>
    </span>
  </CetDialog>
</template>

<script>
import commonApi from "@/api/custom.js";
export default {
  name: "batchSetShiftsDialog",
  props: {
    selectedDate: {
      type: [Number, Date]
    },
    visibleTrigger_in: Number,
    closeTrigger_in: Number,
    inputData_in: Object,
    schedulingPlan: Array,
    schedulingSchemeId: [String, Number]
  },
  data() {
    return {
      defaultValue: [
        this.$moment(this.selectedDate).startOf("month"),
        this.$moment(this.selectedDate).endOf("month")
      ],
      CetDialog_1: {
        showClose: true,
        width: "480px",
        title: "批量设置班次",
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime()
      },
      CetForm_1: {
        dataMode: "component", // 数据获取模式： backendInterface  后端接口 ；其他组件  component   ; 静态数据   static
        queryMode: "trigger", // 查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          writeFunc: "",
          modelLabel: "",
          dataIndex: [], //可以用设置属性path,例如a[0].b可以找到{a:[b:2]}中的值2
          modelList: [],
          groups: [] //例子     {   name: "treenode",   models: ["floor", "building", "sectionarea"] }
        },
        //组件输入项
        inputData_in: {},
        data: {},
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        saveTrigger_in: new Date().getTime(),
        localSaveTrigger_in: new Date().getTime(),
        resetTrigger_in: new Date().getTime(),
        size: "small",
        labelWidth: "120px",
        labelPosition: "top",
        rules: {
          periodOfTime: [
            {
              required: true,
              message: "请选择时段",
              trigger: ["blur", "change"]
            }
          ],
          shiftPlanId: [
            {
              required: true,
              message: "请选择班次",
              trigger: ["blur", "change"]
            }
          ]
        },
        event: { saveData_out: this.CetForm_1_saveData_out }
      },
      CetButton_confirm: {
        visible_in: true,
        disable_in: false,
        title: $T("确定"),
        type: "primary",
        plain: false,
        event: {
          statusTrigger_out: this.CetButton_confirm_statusTrigger_out
        }
      },
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: $T("取消"),
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      ElOption_shiftPlan: {
        options_in: [],
        key: "id",
        value: "id",
        label: "name",
        disabled: "disabled"
      },
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.selectDate = minDate.getTime();
          if (maxDate) {
            this.selectDate = "";
          }
        },
        disabledDate: time => {
          // 获取 selectedDate 的当月第一天和最后一天
          const startOfMonth = this.$moment(this.selectedDate)
            .startOf("month")
            .valueOf();
          const endOfMonth = this.$moment(this.selectedDate)
            .endOf("month")
            .valueOf();
          // 获取当前时间戳
          const currentTime = time.getTime();
          // 判断当前时间是否在当月范围内
          return currentTime < startOfMonth || currentTime > endOfMonth;
        }
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    async visibleTrigger_in(val) {
      this.CetDialog_1.openTrigger_in = val;
      if (val) {
        this.getQueryClassScheme();
      }
    },
    closeTrigger_in(val) {
      this.CetDialog_1.closeTrigger_in = val;
    },
    //设置默认月份
    selectedDate: {
      handler(newVal) {
        this.defaultValue = [
          this.$moment(newVal).startOf("month"),
          this.$moment(newVal).endOf("month")
        ];
      }
    }
  },
  methods: {
    /**
     * 查询班次方案
     */
    async getQueryClassScheme() {
      let params = {
        schedulingSchemeId: this.schedulingSchemeId
      };
      let res = await commonApi.queryClassesscheme(params);
      if (res.code === 0) {
        this.ElOption_shiftPlan.options_in = this._.get(res, "data", []);
      }
    },

    /**
     * 弹窗确认按钮
     */
    CetButton_confirm_statusTrigger_out(val) {
      this.CetForm_1.localSaveTrigger_in = this._.cloneDeep(val);
    },

    /**
     * 弹窗取消按钮
     */
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_1.closeTrigger_in = this._.cloneDeep(val);
      this.clearFormData();
    },

    /**
     *表单保存
     */
    async CetForm_1_saveData_out() {
      const { periodOfTime, shiftPlanId } = this.CetForm_1.data;
      const shiftPlan = this.ElOption_shiftPlan.options_in.find(
        item => item.id === shiftPlanId
      );
      this.$emit("confirmForm", {
        periodOfTime,
        shiftPlan
      });
      this.clearFormData();
      this.CetDialog_1.closeTrigger_in = Date.now();
    },

    /**
     *清除表单和相关数据的方法
     */
    beforeClose() {
      this.clearFormData();
      this.CetDialog_1.closeTrigger_in = Date.now();
    },

    /**
     *清除表单和相关数据的方法
     */
    clearFormData() {
      this.CetForm_1.data = {};
      this.ElOption_shiftPlan.options_in = [];
    }
  }
};
</script>

<style lang="scss" scoped>
.CetDialog {
  :deep(.el-dialog__body) {
    @include background_color(BG);
    @include padding(J1);
  }
  :deep(.el-dialog__body) {
    display: flex;
  }
  :deep(.el-form-item) {
    .el-form-item__label {
      line-height: 24px;
      text-align: left;
    }
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
</style>
