<template>
  <el-tooltip :placement="placement" :disabled="hiddenTooltip" :effect="effect">
    <div slot="content" class="content">
      {{ content }}
    </div>
    <span
      class="longStr"
      id="text"
      @mouseenter="isHiddenTooltip($event)"
      @click="clickText"
    >
      {{ text }}
    </span>
  </el-tooltip>
</template>

<script>
export default {
  name: "textTooltip",
  props: {
    placement: {
      type: String,
      default: "top"
    },
    content: {
      type: [String, Number]
    },
    text: {
      type: [String, Number]
    },
    effect: {
      type: String,
      default: "dark"
    },
    determineHeight: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      hiddenTooltip: true
    };
  },
  methods: {
    isHiddenTooltip(event) {
      const ev = event.target;
      const ev_weight = ev.scrollWidth; // 文本的实际宽度   scrollWidth：对象的实际内容的宽度，不包边线宽度，会随对象中内容超过可视区后而变大。
      const content_weight = ev.clientWidth; // 文本的可视宽度 clientWidth：对象内容的可视区的宽度，不包滚动条等边线，会随对象显示大小的变化而改变。
      const ev_height = ev.scrollHeight; // 文本的实际高度
      const content_height = ev.clientHeight; // 文本的可视高度
      // const content_weight = this.$refs.tlp.$el.parentNode.clientWidth; // 文本容器宽度(父节点)
      if (
        ev_weight > content_weight ||
        (this.determineHeight && ev_height > content_height)
      ) {
        // 实际宽度 > 可视宽度 || 实际高度 > 可视高度 文字溢出
        this.hiddenTooltip = false;
      } else {
        // 否则为不溢出
        this.hiddenTooltip = true;
      }
    },
    clickText() {
      this.$emit("clickText");
    }
  }
};
</script>

<style lang="scss" scoped>
.longStr {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
